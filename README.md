<div align="center">

# 🌐 Universal Engine

> 🚀 A cutting-edge **modular monolithic** backend service powered by **ElysiaJS** and **Bun runtime**, designed for the **Unifi eXperience Engine**

[![Version](https://img.shields.io/badge/version-0.0.1-blue?style=for-the-badge)](https://github.com/tmberhad-unifi/universal-engine)
[![Bun](https://img.shields.io/badge/bun-latest-orange?style=for-the-badge&logo=bun)](https://bun.sh)
[![ElysiaJS](https://img.shields.io/badge/elysiajs-1.3.1-green?style=for-the-badge)](https://elysiajs.com)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.7+-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Enabled-blue?style=for-the-badge&logo=docker)](https://www.docker.com/)

**408 TypeScript files** • **187 directories** • **15 modules** • **4-level security**

</div>

---

<div align="center">

### 🎨 Quick Links
[📃 API Documentation (Scalar)](https://myunifi-dev.myu.unifi.com.my/ue/login) •
[🗄️ Database Studio](http://localhost:4983) •
[📊 Health Check](http://localhost:3000/health) •
[🐳 Docker Hub](https://hub.docker.com) •
[📧 Support](mailto:<EMAIL>)

</div>

## 🎯 System Architecture

### 🏗️ Modular Monolithic Design
Transitioned from microservices to modular monolithic architecture for better maintainability, reduced complexity, and centralized logging.

### 🔄 Core Components
- **🌐 Client Layer**: HTTP/REST API endpoints with 4-level security
- **🚀 Universal Engine**: Main application service with 15 modules
- **📊 OpenTelemetry**: Distributed tracing with Axiom integration
- **💾 PostgreSQL**: Primary database with DrizzleORM
- **⚡ Redis Cache**: High-performance caching and session storage
- **🛡️ UAID**: Universal Authentication and Identity Directory

### 🔗 Integration Ecosystem
- **📡 WSO2 ESB**: 50+ API endpoints for telecommunications services
- **🎯 WERAS**: Customer analytics, rewards, and billing
- **📺 OMG**: OTT service management (Netflix, Disney+)
- **💼 BIZ**: Business services (Cloud Connect, eCommerce)
- **📧 Email Services**: SendGrid integration with templates
- **⏱️ Temporal**: Workflow orchestration and automation

## ⚡ Tech Stack

### 🏃‍♂️ Runtime & Framework
- **🎯 Bun Runtime**: Latest version for high performance
- **⚡ ElysiaJS 1.3.1**: Modern TypeScript web framework
- **📝 TypeScript 5.7+**: Full type safety across 408 files

### 📊 Data & Caching
- **💾 PostgreSQL**: Primary database with connection pooling
- **🔄 DrizzleORM**: Type-safe database operations
- **📦 Redis (ioredis)**: High-performance caching and sessions

### 🛡️ Security & Monitoring
- **🔐 4-Level API Security**: Private, Protected, Public, Internal
- **📊 OpenTelemetry**: Distributed tracing with Axiom
- **🔍 Pino Logging**: Structured logging with @bogeychan/elysia-logger
- **🛡️ PII Encryption**: AES encryption for sensitive data

### 🛠️ Development & DevOps
- **📚 Scalar Documentation**: Interactive API documentation
- **📖 Drizzle Studio**: Database management GUI
- **🧪 Vitest**: Fast unit testing framework
- **🧹 Biome**: Modern linting and formatting
- **🐳 Docker**: Containerization with multi-environment support
- **🚀 GitHub Actions**: Automated CI/CD pipeline

## 🛠️ Prerequisites

### 🎨 Required Tools

| Tool | Version | Purpose |
|------|---------|----------|
| 🏃 **Bun** | Latest | Runtime & Package Manager |
| 🐳 **Docker** | Latest | Containerization & Services |
| 🌳 **Git** | Latest | Version Control |
| 💻 **VS Code** | Latest | Development IDE (Recommended) |

### 🎯 Recommended VS Code Extensions

```json
{
  "recommendations": [
    "biomejs.biome",           // Modern linting & formatting
    "ms-vscode.vscode-docker", // Docker support
    "bradlc.vscode-tailwindcss", // Tailwind CSS support
    "ms-vscode.vscode-json",   // JSON support
    "ms-vscode.vscode-typescript-next" // TypeScript support
  ]
}
```

## 🚀 Getting Started

### 1️⃣ Clone & Setup
```bash
# 📥 Clone repository
git clone https://github.com/tmberhad-unifi/universal-engine.git
cd universal-engine

# 📦 Extract environment files
unzip environments-*.zip  # 🔑 Password: #HappyCodingBuddy
cp .env.dev .env

# 📥 Install dependencies
bun install
```

### 2️⃣ Start Services
```bash
# 🐳 Start PostgreSQL & Redis
docker-compose up -d

# 🗄️ Run database migrations
bun db:up

# 🏃 Start development server
bun dev
```

### 3️⃣ Verify Installation
- **🌐 API Server**: http://localhost:3000
- **📃 API Documentation**: http://localhost:3000/scalar
- **📊 Health Check**: http://localhost:3000/health
- **🗄️ Database Studio**: `bun db:studio` (http://localhost:4983)

### 🛠️ Development Commands

| Command | Description | Environment |
|---------|-------------|-------------|
| `bun dev` | Development server | Local |
| `bun sit` | SIT environment | Testing |
| `bun uat` | UAT environment | Testing |
| `bun preprod` | Pre-production | Staging |
| `bun test` | Run unit tests | All |
| `bun lint` | Code linting | All |
| `bun lint:fix` | Fix lint issues | All |

### 🗄️ Database Commands

| Command | Description |
|---------|-------------|
| `bun db:generate` | Generate SQL schema |
| `bun db:up` | Run migrations |
| `bun db:studio` | Database GUI |
| `bun db:drop` | Drop schema |

## 🏗️ Project Structure

### 📁 Root Directory (408 TypeScript files, 187 directories)
```
universal-engine/
├── 📂 src/                  # Source code (408 TS files)
│   ├── 📂 config/          # Database, cache, logging config
│   ├── 📂 enum/            # TypeScript enums & constants
│   ├── 📂 integration/     # External service integrations
│   │   ├── temporal/       # Workflow orchestration
│   │   ├── omg/           # OTT media gateway
│   │   ├── wso2/          # Enterprise service bus
│   │   ├── weras/         # Analytics & rewards
│   │   └── emailTemplate/ # Email services
│   ├── 📂 middleware/      # Authentication & error handling
│   │   ├── auth.ts        # Multi-level API security
│   │   ├── error.ts       # UE_ERROR class
│   │   └── uaid/          # Identity directory
│   ├── 📂 modules/         # 15 business modules
│   │   ├── address/       # Address & coverage
│   │   ├── catalogue/     # Product catalogs
│   │   ├── order/         # Order management
│   │   ├── payment/       # Payment processing
│   │   ├── user/          # User management
│   │   ├── temporal/      # Workflow management
│   │   └── ...           # 9 more modules
│   └── 📂 shared/         # Common utilities & schemas
├── 📂 environments/        # Environment configs (.env.*)
├── 📂 postgres/           # Database initialization
├── 📄 drizzle.config.ts   # Database configuration
├── 📄 docker-compose.yaml # Service orchestration
└── 📄 package.json       # Dependencies & scripts
```

### 🧩 Module Architecture
Each module follows a consistent structure:
```
modules/{module-name}/
├── index.ts              # Route exports
├── v1/
│   ├── controllers/      # API endpoints
│   ├── services/         # Business logic
│   ├── schemas/          # Type definitions
│   │   ├── api/         # Request/response schemas
│   │   └── models/      # Database models
│   └── helpers/         # Utility functions
```

## 🔐 API Security Levels

Universal Engine implements a **4-level security model**:

| Level | 🔒 Auth Required | Usage | Examples |
|-------|------------------|-------|----------|
| **Private** | Bearer Token + API Key | Sensitive operations | User data, payments, orders |
| **Protected** | API Key only | Trusted integrations | Callbacks, webhooks |
| **Public** | None | Open access | Health checks, public catalogs |
| **Internal** | Internal Key + IP | Development tools | Cache management, debugging |

### 🛡️ Security Features
- **🔐 PII Encryption**: AES encryption for sensitive data
- **🔑 Token Validation**: Bearer token with regex matching
- **🌐 IP Restrictions**: Internal API access control
- **📊 Request Tracking**: Integration ID for all requests
- **🛡️ UAID Integration**: Universal Authentication and Identity Directory

## 🧩 Core Modules (15 Total)

| Module | Purpose | Key Features |
|--------|---------|--------------|
| **Address** | Location services | Granite integration, coverage checking |
| **Catalogue** | Product management | Add-ons, OTT, TV packages |
| **Order** | Order processing | Stock checking, OTT activation |
| **Payment** | Payment handling | Autopay, OSES, bank integration |
| **User** | Account management | Profiles, billing, SME accounts |
| **Temporal** | Workflow automation | Task orchestration, decisions |
| **Record** | Service tracking | Activity logs, service requests |
| **Reward** | Loyalty program | WERAS integration, redemption |
| **Notification** | Communications | Email, SMS, TAC management |
| **Eligibility** | Service validation | Customer eligibility checks |
| **Util** | System utilities | Alerts, config, reference data |
| **Security** | Security operations | Validation, compliance |
| **Setting** | Configuration | App settings, preferences |
| **Batch** | Background tasks | Scheduled operations |
| **Internal** | Development tools | Cache operations, debugging |

## ❓ Troubleshooting

### 🐳 Docker Issues
```bash
# 🔄 Reset Docker environment
docker-compose down -v
docker-compose up -d --build
```
#### Issue 1: `init-db` Fails on macOS Due to Permission Denied (MacOS Users Only)
```bash
permission denied: ./postgres/init-db.sh
```
##### Cause:

On macOS, the init-db.sh script may not have the proper execution permissions, causing Docker to fail when trying to run it.

##### Solution:

1. Grant execution permission to the script using the following command:
```bash
chmod +x ./postgres/init-db.sh
```
2. Then, restart your Docker container:
```bash
docker-compose restart
```

#### Issue 2: `init-db` Container Won’t Start
```bash
/usr/local/bin/docker-entrypoint.sh: line 351: /docker-entrypoint-initdb.d/init-db.sh: cannot execute: required file not found
```
##### Cause:

This typically occurs on Windows, where the line endings of the `postgres/init-db.sh` file are converted from Unix (LF) to Windows (CRLF).

##### Solution:

Convert the `init-db.sh` file back to Unix format. Here's how with **Notepad++**:

1. Open the `init-db.sh` file in Notepad++.
2. Go to **Edit > EOL Conversion > Unix (LF)**.
3. Save the file.

Here's how to do it in **VSCode**:

1. Open the `init-db.sh` file in VSCode.
2. Go to rigth side of Status Bar (The bar at the bottom of the window), the End of Line Sequence option is beside the encoding option
   ![Status Bar](/docs/img/crlf.png)
3. Once clicked, options will be shown on the top of the window. Select LF
   ![CRLF Option](/docs/img/crlf-option.png)

#### Issue 3: Docker Desktop Fails to Start (Windows Users Only)

##### Cause:

This typically occurs when you don't have the admin privileges to run Docker Desktop.

##### Solution:

1. **Run PowerShell as Administrator:**
   Open PowerShell with administrative privileges.

2. **Enable Windows Features:**
   Run the following commands to enable required features:
   ```bash
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
   dism.exe /online /enable-feature /featurename:Microsoft-Hyper-V /all /norestart
   ```

3. **Add Your Account to the Docker Users Group:**
   Replace `${your_account}` with your username and run:
   ```bash
   net localgroup docker-users "tmmaster\${your_account}" /add
   ```

4. **Enable Hyper-V:**
   ```bash
   bcdedit /set hypervisorlaunchtype auto
   ```

5. **Install WSL (Windows Subsystem for Linux):**
   ```bash
   wsl --set-default-version 2
   wsl --install -d Ubuntu
   ```
   Ensure the installation is performed in your user directory.

6. **Restart Your Computer:**
   Reboot your system to apply the changes.

After completing these steps, Docker Desktop should start successfully.

---

## 🎯 Enterprise Support

### 📧 Integration Support Contacts
- **🎯 WERAS Integration**: <EMAIL>
- **📡 WSO2 Enterprise Services**: <EMAIL>
- **📺 OMG Media Gateway**: <EMAIL>

### 🔗 Quick Access Links
- **📃 API Documentation**: [Scalar Docs](https://myunifi-dev.myu.unifi.com.my/ue/login)
- **🗄️ Database Studio**: `bun db:studio` → http://localhost:4983
- **📊 Health Check**: http://localhost:3000/health
- **🐳 Docker Services**: `docker-compose ps`

---

<div align="center">

### 🌟 Project Status
![Status](https://img.shields.io/badge/Status-Production_Ready-green?style=for-the-badge)
![Version](https://img.shields.io/badge/Version-0.0.1-blue?style=for-the-badge)
![Files](https://img.shields.io/badge/TypeScript_Files-408-orange?style=for-the-badge)
![Modules](https://img.shields.io/badge/Modules-15-purple?style=for-the-badge)

### 📫 Support & Contact

[![GitHub Issues](https://img.shields.io/badge/Open_Issues-GitHub-green?style=for-the-badge&logo=github)](https://github.com/tmberhad-unifi/universal-engine/issues/new)
[![Email Support](https://img.shields.io/badge/Email-Support-red?style=for-the-badge&logo=mail.ru)](mailto:<EMAIL>)

**Made with 💖 by the Universal Engine Team** 🚀

*Powering Unifi's customer experience with cutting-edge technology*

</div>
