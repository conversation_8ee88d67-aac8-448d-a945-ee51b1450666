# Universal Engine - Complete LLM Context Documentation

## 🌐 Project Overview

Universal Engine is a cutting-edge backend service powered by ElysiaJS and Bun runtime, designed for the Unifi eXperience Engine. It serves as a comprehensive API service that handles telecommunications and customer management operations, having transitioned from a microservices architecture to a **modular monolithic** approach for better maintainability and reduced complexity.

## 🛠️ Tech Stack

- **Runtime**: Bun (Latest)
- **Framework**: ElysiaJS 1.3.1
- **Language**: TypeScript 5.7+
- **Database**: PostgreSQL with DrizzleORM
- **Cache**: Redis (ioredis)
- **Monitoring**: OpenTelemetry with Axiom
- **Logging**: Pino with @bogeychan/elysia-logger
- **Testing**: Vitest
- **Linting**: Biome
- **Containerization**: Docker

## 🏗️ System Architecture

### Core Components
- **🌐 Client Layer**: HTTP/REST API endpoints
- **🚀 Universal Engine**: Main application service (modular monolithic)
- **📊 OpenTelemetry**: Distributed tracing system with Axiom integration
- **💾 PostgreSQL**: Primary database with structured nested data support
- **⚡ Redis Cache**: High-performance caching layer

### Integration Points
- **🔒 Token Authentication**: Bearer token validation with UAID
- **🌍 CORS-enabled endpoints**: Cross-origin resource sharing
- **📡 Real-time monitoring**: OpenTelemetry tracing to Axiom
- **🔍 Advanced logging**: Pino structured logging with request/response tracking
- **🔐 Multi-level Security**: Private, Protected, Public, and Internal API access levels

## 📁 Project Structure

### Root Directory
```
universal-engine/
├── 📂 .github/              # CI/CD pipeline configuration
├── 📂 .husky/               # Pre-commit hooks automation
├── 📂 environments/         # Environment configuration files (.env.dev, .env.sit, etc.)
├── 📂 postgres/             # Database initialization scripts
├── 📂 src/                  # Source code (408 TypeScript files)
├── 📄 env.config.json       # Backend URLs for different environments
├── 📄 drizzle.config.ts     # Database configuration
├── 📄 docker-compose.yaml   # Docker services configuration
├── 📄 package.json          # Project dependencies and scripts
└── 📄 README.md            # Project overview and setup instructions
```

### Source Code Structure (187 directories, 408 TypeScript files)
```
src/
├── 📂 config/              # Application configuration
│   ├── db.config.ts        # PostgreSQL connection with pooling
│   ├── cache.config.ts     # Redis configuration
│   ├── env.config.ts       # Environment variables management
│   └── pinoLog.config.ts   # Logging configuration
├── 📂 enum/                # TypeScript enums and constants
├── 📂 integration/         # External service integrations
│   ├── temporal/          # Temporal workflow integration
│   ├── omg/               # Over-the-top Media Gateway
│   ├── wso2/              # WSO2 Enterprise Service Bus
│   ├── weras/             # WERAS system integration
│   ├── biz/               # Business integrations
│   └── emailTemplate/     # Email template services
├── 📂 middleware/          # Custom middleware
│   ├── auth.ts            # Authentication and authorization
│   ├── error.ts           # Error handling (UE_ERROR class)
│   └── uaid/              # Universal Authentication and Identity Directory
├── 📂 modules/             # API modules (business logic)
│   ├── address/           # Address and coverage services
│   ├── batch/             # Batch processing
│   ├── catalogue/         # Product catalogs
│   ├── eligibility/       # Customer eligibility checks
│   ├── notification/      # Email and SMS notifications
│   ├── order/             # Order management and tracking
│   ├── payment/           # Payment processing
│   ├── record/            # Customer records and service requests
│   ├── reward/            # Loyalty and rewards program
│   ├── security/          # Security operations
│   ├── setting/           # Application settings
│   ├── temporal/          # Temporal workflow management
│   ├── user/              # User profile and account management
│   ├── util/              # Utility services
│   └── internal/          # Internal development tools
├── 📂 shared/              # Shared utilities and schemas
│   ├── cache.ts           # Caching utilities
│   ├── common.ts          # Common helper functions
│   ├── encryption/        # Encryption utilities
│   ├── scalar.ts          # API documentation configuration
│   └── schemas/           # Shared API schemas
├── 📄 index.ts             # Main application entry point
└── 📄 routes.ts            # API route definitions and guards
```

## 🧩 Universal Engine Modules

### 1. Address Module (`src/modules/address/`)
- **Purpose**: Address and coverage services
- **Features**: Granite address integration, location services, coverage checking
- **Controllers**: Address validation and management

### 2. Batch Module (`src/modules/batch/`)
- **Purpose**: Batch processing and scheduled operations
- **Features**: Failed notification email scheduler, OneSignal notification fetcher, Telegram reporting

### 3. Catalogue Module (`src/modules/catalogue/`)
- **Controllers**: Add-ons, OTT, TV Pack management
- **Features**: Smart Device, Smart Home, Mesh Wifi, UPB, TV Pack, Blacknut, Netflix, Disney+
- **Database Tables**: `addons_metadata`, `addons_catalogue`, `tv_pack_catalogue`, `ott_plan_catalogue`

### 4. Eligibility Module (`src/modules/eligibility/`)
- **Controllers**: Add-ons Eligibility, AWC MSR, FSU, Rebate, Service Request, SSM, Survey
- **Features**: Customer eligibility validation for various services
- **Database Tables**: `addons_reporting`, `rebate_redeem_eligibility`

### 5. Notification Module (`src/modules/notification/`)
- **Controllers**: Email Controller, TAC Controller
- **Features**: Email templates, SMS notifications, Terms and Conditions management
- **Database Tables**: `tac_counter`

### 6. Order Module (`src/modules/order/`)
- **Controllers**: Orderable, OTT, OTT Callback, Stock management
- **Features**: Product ordering, stock checking, OTT activation, callback handling
- **Database Tables**: `orderable`, `ott_order`, `non_orderable_txn_history`, `siebel_product_map`

### 7. Payment Module (`src/modules/payment/`)
- **Controllers**: Autopay, OSES, OSES Callback, OSES Pre-login
- **Features**: Payment processing, autopay setup, bank integration, transaction history
- **Database Tables**: `bank_list`, `autopay_setting_history`, `bill_payment_txn_history`, `oses_txn_history`

### 8. Record Module (`src/modules/record/`)
- **Controllers**: Activity Tracker, Add-ons Record, Easyfix, Order Tracker, Rebate, Service Request, Survey, VOC Callback
- **Features**: Customer activity tracking, service request management, survey handling
- **Database Tables**: `appointment_counter`, `appointment_txn_history`, `nes_survey`, `service_request_history`

### 9. Reward Module (`src/modules/reward/`)
- **Controllers**: Rewards Controller for customer loyalty program
- **Features**: WERAS integration, loyalty points, reward redemption, item management
- **Database Tables**: `rewards_template`

### 10. Security Module (`src/modules/security/`)
- **Purpose**: Security-related operations and validations

### 11. Setting Module (`src/modules/setting/`)
- **Purpose**: Application and user settings management

### 12. Temporal Module (`src/modules/temporal/`)
- **Controllers**: Account, Address, Decision, Demand, Notification, Order, SLOF, User Task
- **Features**: Workflow management, decision automation, task orchestration
- **Database Tables**: `mail_notification`, `temporal_decision`, `temporal_user_task`

### 13. User Module (`src/modules/user/`)
- **Controllers**: Billing Account, Easyfix, KCI, Linked Account, Pay For Anyone, Profile Account, SME Account
- **Features**: Profile management, billing information, account linking, SME services
- **Database Tables**: `account_settings`, `linked_accounts`

### 14. Util Module (`src/modules/util/`)
- **Controllers**: Alert, Config, LOV (List of Values)
- **Features**: System configuration, alert management, reference data
- **Database Tables**: Various LOV tables for addresses, alerts, complaints

### 15. Internal Module (`src/modules/internal/`)
- **Purpose**: Internal development tools and cache management
- **Controllers**: Redis Controller for cache operations
- **Features**: Cache debugging, internal utilities

## 🛡️ Middleware Layer

### 1. Authentication Middleware (`src/middleware/auth.ts`)
- **validateToken**: Bearer token validation with regex matching
- **validateApiKey**: API key validation against environment variables
- **restrictToInternal**: Internal system access control with IP filtering

### 2. Error Middleware (`src/middleware/error.ts`)
- **UE_ERROR Class**: Custom error handling with status codes
- **Features**: Standardized error responses, integration ID tracking, automatic error logging

### 3. UAID Middleware (`src/middleware/uaid/`)
Universal Authentication and Identity Directory with comprehensive user management:
- **Identity Management**: User identity creation, validation, and management
- **Profile Services**: User profile CRUD operations with encryption
- **Session Management**: Session validation, creation, and expiration
- **SME Services**: Small and Medium Enterprise identity handling
- **TAC Management**: Terms and Conditions acceptance tracking

## 🔌 Integration Layer

### 1. Temporal Integration (`src/integration/temporal/`)
- **Workflow Management**: Temporal workflow orchestration
- **Features**: Task scheduling, workflow execution, decision automation

### 2. OMG Integration (`src/integration/omg/`)
Over-the-top Media Gateway for streaming services:
- **Netflix Services**: Activation, plan changes, account recovery, cancellation
- **Disney+ Services**: Mobile number changes, bundle management
- **OTT Management**: Subscription management, entitlement verification

### 3. WSO2 Integration (`src/integration/wso2/`)
Enterprise Service Bus integration:
- **Address Services**: Granite address validation
- **Eligibility Services**: Customer eligibility checks
- **Notification Services**: SMS and email delivery
- **Order Services**: Order management and tracking
- **Payment Services**: Payment processing
- **Record Services**: Customer records and service requests
- **User Services**: Customer account management

### 4. WERAS Integration (`src/integration/weras/`)
Wireless Enterprise Resource and Analytics System:
- **Customer Services**: Bills retrieval, membership information
- **Rewards System**: Promotions, loyalty points, reward redemption
- **Analytics**: Transaction history, customer reporting

### 5. BIZ Integration (`src/integration/biz/`)
- **Cloud Connect**: Business cloud connectivity services
- **eCommerce**: E-commerce platform integration

### 6. Email Template Integration (`src/integration/emailTemplate/`)
- **Autopay Templates**: Automatic payment email templates
- **OSES Templates**: Payment service email templates
- **OTT Email Templates**: Streaming service notifications

## ⚙️ Configuration Management

### Environment Configuration (`src/config/env.config.ts`)
Comprehensive environment variable management with 150+ variables:
- **Database**: PostgreSQL connection parameters
- **WSO2 Endpoints**: 50+ API endpoints and authentication tokens
- **External Services**: WERAS, OMG, BIZ, Email service URLs
- **Authentication**: API keys, internal keys, JWT secrets
- **Monitoring**: Axiom tokens, OpenTelemetry configuration

### Database Configuration (`src/config/db.config.ts`)
- **PostgreSQL**: Connection pooling with configurable pool size
- **SSL Configuration**: Environment-specific SSL settings
- **Connection Management**: Graceful connection lifecycle with health checks

### Cache Configuration (`src/config/cache.config.ts`)
- **Redis Setup**: Connection configuration and clustering
- **Caching Strategies**: TTL management, cache invalidation
- **Features**: Distributed caching, session storage, rate limiting support

### Logging Configuration (`src/config/pinoLog.config.ts`)
- **Structured Logging**: JSON-formatted logs with Pino
- **Environment-specific**: Debug level for dev, info for production
- **Features**: Request/response logging, error tracking, performance monitoring

## 🔐 API Security Levels

### 1. Private API (🔒) - Maximum Security
- **Authentication**: BOTH Bearer Token AND API Key required
- **Usage**: Highly sensitive operations (user data, payments, orders)
- **Headers Required**: `Authorization: Bearer <token>`, `x-api-key: <key>`, `source`, `segment`
- **Guard Implementation**: `validateToken()` + `validateApiKey()`

### 2. Protected API (🔐) - Trusted Integration
- **Authentication**: API Key only
- **Usage**: Trusted integrations and server-to-server communication
- **Headers Required**: `x-api-key: <key>`, `source`, `segment`
- **Guard Implementation**: `validateApiKey()`

### 3. Public API (🌐) - Open Access
- **Authentication**: No authentication required
- **Usage**: Non-sensitive operations, public information
- **Features**: Rate limiting, CORS enabled

### 4. Internal API (🛠️) - Development & Operations
- **Authentication**: Internal API key + IP restriction
- **Usage**: Developer tools, system administration, cache management
- **Access Control**: IP whitelist, internal network only
- **Guard Implementation**: `restrictToInternal()`

## 🗄️ Database Schema & Architecture

### Database Design Principles
- **Naming Convention**: All tables and columns use snake_case
- **Standard Columns**: Every table has `id`, `created_at`, `updated_at`
- **Data Types**: JSON columns for nested data, proper indexing
- **Encryption**: PII data encrypted before storage, ID values hashed
- **Relationships**: Foreign key constraints, proper normalization

### Key Database Tables

#### Catalogue & Product Management
- `addons_catalogue`: Product catalog for add-on services
- `addons_metadata`: Metadata for add-on categories with FAQ, TNC, warranty URLs
- `tv_pack_catalogue`: Television package information and pricing
- `ott_plan_catalogue`: Streaming service plans (Netflix, Disney+, etc.)

#### User & Account Management
- `account_settings`: User account preferences and quick links
- `linked_accounts`: Account linking information for Pay For Anyone
- `consumer_accounts`: Customer account details (encrypted)
- `service_accounts`: Service-specific account information
- `sme_accounts`: Small and Medium Enterprise account data

#### Order & Transaction Management
- `orderable`: Product ordering information and availability
- `ott_order`: Streaming service orders and activation status
- `non_orderable_txn_history`: Transaction logs for non-orderable items
- `siebel_product_map`: Product mapping for Siebel integration

#### Payment & Billing
- `bank_list`: Supported banks for payments with flags
- `autopay_setting_history`: Automatic payment configuration history
- `bill_payment_txn_history`: Bill payment transaction records
- `oses_txn_history`: OSES payment gateway transaction logs

#### Workflow & Temporal
- `temporal_decision`: Decision points in workflows
- `temporal_user_task`: User task instances and status
- `temporal_user_task_definition`: Task templates and definitions

#### System & Configuration
- `rewards_template`: Loyalty program templates
- `force_update`: App version control and forced updates
- `maintenance_page`: Maintenance mode configuration
- Various LOV (List of Values) tables for dropdowns and reference data

### PII (Personally Identifiable Information) Handling
**Security Measures**:
1. ID types and values retrieved through session ID
2. Account numbers sent encrypted in API requests
3. All PII (except ID values) encrypted before database storage
4. ID values are hashed instead of encrypted
5. No PII data in logs (except intercept logs for debugging)

## 🛠️ Development Commands

### Universal Engine Commands
```bash
# Development
bun dev              # Start development server with hot reload
bun sit              # Start SIT environment
bun uat              # Start UAT environment
bun preprod          # Start preprod environment
bun compose          # Start with Docker Compose
bun test             # Run tests with Vitest
bun lint             # Run Biome linter
bun lint:fix         # Fix linting issues

# Database Operations
bun db:generate      # Generate SQL schema with Drizzle
bun db:up            # Run database migrations
bun db:migrate       # Apply migrations
bun db:studio        # Start Drizzle Studio (database GUI)
bun db:drop          # Drop database schema

# Utilities
bun trace            # Generate TypeScript trace for performance
bun depcheck         # Check for unused dependencies
bun cleanup          # Clean and update all dependencies
```

## 📜 Development Rules & Guidelines

### Code Standards
- **Naming Conventions**:
  - Use **Camel Case** for constants/variables
  - Use **Pascal Case** for types and classes
  - Use **snake_case** for table and column names
  - All folder and file names are lowercase
- **Type Safety**: Avoid `any` or `unknown` unless absolutely necessary and justified
- **Comments**: Provide meaningful context, explain "why" not "what"
- **PII Handling**: All PII must be encrypted before leaving the API

### Table Standards
- All tables must have `id`, `created_at`, and `updated_at` columns
- Use snake_case for all table and column names
- Proper indexing and foreign key constraints

## 🌟 Key Features

### Universal Engine Features
1. **🔄 Modular Monolithic Architecture**: Transitioned from microservices for better maintainability
2. **🔐 Multi-level Security**: Private, Protected, Public, and Internal API access levels
3. **🔌 Extensive Integrations**: WSO2, WERAS, OMG, BIZ, Email services, Temporal workflows
4. **📊 Real-time Monitoring**: OpenTelemetry with Axiom integration for distributed tracing
5. **🗄️ Advanced Database**: PostgreSQL with DrizzleORM, JSON support, encryption
6. **⚡ High Performance**: Bun runtime, Redis caching, connection pooling
7. **🛡️ Security First**: PII encryption, token validation, IP restrictions
8. **📝 Type Safety**: Full TypeScript implementation with strict typing (408 files)
9. **🐳 Containerization**: Docker support with multi-environment configuration
10. **🔄 CI/CD Pipeline**: GitHub Actions with automated testing and deployment

### Integration Ecosystem
- **📡 WSO2 ESB**: 50+ API endpoints for core telecommunications services
- **🎯 WERAS**: Customer analytics, rewards, and billing integration
- **📺 OMG**: OTT service management (Netflix, Disney+, streaming platforms)
- **💼 BIZ**: Business services (Cloud Connect, eCommerce platforms)
- **📧 Email Services**: SendGrid integration with template management
- **⏱️ Temporal**: Workflow orchestration and task automation
- **🔄 Real-time Sync**: Live data synchronization across all platforms

## 🎯 Enterprise API Support Contacts

**For technical support and integration assistance:**

📌 **WERAS Integration** – 📧 <EMAIL>
📌 **WSO2 Enterprise Services** – 📧 <EMAIL>  
📌 **OMG Media Gateway** – 📧 <EMAIL>

*For faster resolution, include error messages, request payloads, and timestamps in your support requests.*

---

## 📋 Summary

Universal Engine serves as the backbone for Unifi's customer experience platform, providing:

- **🚀 High-performance backend**: 408 TypeScript files across 187 directories with modular architecture
- **🔐 Enterprise security**: Multi-level authentication with PII encryption and secure integrations
- **📊 Comprehensive monitoring**: OpenTelemetry tracing, structured logging, and real-time observability
- **🔌 Extensive integrations**: WSO2, WERAS, OMG, and other enterprise systems
- **🗄️ Robust data management**: PostgreSQL with DrizzleORM, Redis caching, and proper schema design
- **⚡ Modern tech stack**: Bun runtime, ElysiaJS framework, and cutting-edge development tools

The platform delivers robust, scalable, and secure telecommunications services with modern development practices, comprehensive API coverage, and unified operations management.
