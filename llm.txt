# Universal Engine - LLM Context Documentation

## Project Overview
Universal Engine is a cutting-edge backend service powered by ElysiaJS and Bun runtime, designed for the Unifi eXperience Engine. It's a comprehensive API service that handles various telecommunications and customer management operations.

## Tech Stack
- **Runtime**: Bun (Latest)
- **Framework**: ElysiaJS 1.3.1
- **Language**: TypeScript 5.7+
- **Database**: PostgreSQL with Dr<PERSON>zle<PERSON>M
- **Cache**: Redis (ioredis)
- **Monitoring**: OpenTelemetry with Axiom
- **Logging**: Pino
- **Testing**: Vitest
- **Linting**: Biome
- **Containerization**: Docker

## System Architecture

### Core Components
- **Client Layer**: HTTP/REST API endpoints
- **Universal Engine**: Main application service
- **OpenTelemetry**: Distributed tracing system
- **PostgreSQL**: Primary database
- **Redis Cache**: High-performance caching

### Integration Points
- **Token Authentication**: Bearer token validation
- **CORS-enabled endpoints**: Cross-origin resource sharing
- **Real-time monitoring**: OpenTelemetry tracing
- **Advanced logging**: Pino structured logging

## Project Structure

```
universal-engine/
├── src/
│   ├── config/          # Application configuration
│   ├── enum/            # TypeScript enums
│   ├── integration/     # External service integrations
│   ├── middleware/      # Custom middleware (auth, error handling, UAID)
│   ├── modules/         # API modules (business logic)
│   └── shared/          # Shared utilities and schemas
├── environments/        # Environment configuration files
├── postgres/           # Database initialization scripts
├── docs/              # Documentation
└── package.json       # Project dependencies and scripts
```

## Main Modules

### 1. Address Module (`src/modules/address/`)
- Handles address-related operations
- Granite address integration
- Location services

### 2. Batch Module (`src/modules/batch/`)
- Batch processing operations
- Bulk data handling

### 3. Catalogue Module (`src/modules/catalogue/`)
- **Add-ons Controller**: Smart Device, Smart Home, Mesh Wifi, UPB, TV Pack, Blacknut
- **OTT Controller**: Over-the-top streaming services
- **TV Pack Controller**: Television package management
- Database tables: `addons_metadata`, `addons_catalogue`, `tv_pack_catalogue`, `ott_plan_catalogue`

### 4. Eligibility Module (`src/modules/eligibility/`)
- **Add-ons Eligibility**: Customer eligibility for add-on services
- **AWC MSR**: Automatic Wireless Coverage and Minimum Service Requirements
- **FSU**: Fiber Service Unit eligibility
- **Rebate**: Rebate eligibility checking
- **Service Request**: SR eligibility validation
- **SSM**: Small and Medium Enterprise eligibility
- **Survey**: Customer survey eligibility

### 5. Notification Module (`src/modules/notification/`)
- **Email Controller**: Email notification services
- **TAC Controller**: Terms and Conditions notifications
- Database tables: `tac_counter`

### 6. Order Module (`src/modules/order/`)
- **Orderable Controller**: Product ordering system
- **OTT Controller**: Streaming service orders (Netflix, Disney+, etc.)
- **OTT Callback Controller**: Callback handling for OTT services
- **Stock Controller**: Inventory and stock management
- Database tables: `orderable`, `ott_order`, `non_orderable_txn_history`, `siebel_product_map`

### 7. Payment Module (`src/modules/payment/`)
- **Autopay Controller**: Automatic payment setup and management
- **OSES Controller**: Online Secure Electronic Services payment
- **OSES Callback Controller**: Payment callback handling
- **OSES Pre-login Controller**: Pre-authentication payment services
- Database tables: `bank_list`, `autopay_setting_history`, `bill_payment_txn_history`, `oses_txn_history`

### 8. Record Module (`src/modules/record/`)
- **Activity Tracker**: Customer activity tracking
- **Add-ons Record**: Add-on service records
- **Easyfix**: Technical support records
- **Order Tracker**: Order status tracking
- **Rebate**: Rebate processing records
- **Service Request**: Customer service request management
- **Survey**: Customer survey management
- **VOC Callback**: Voice of Customer callback handling

### 9. Reward Module (`src/modules/reward/`)
- **Rewards Controller**: Customer loyalty and rewards program
- Database tables: `rewards_template`

### 10. Security Module (`src/modules/security/`)
- Security-related operations and validations

### 11. Setting Module (`src/modules/setting/`)
- Application and user settings management

### 12. Temporal Module (`src/modules/temporal/`)
- **Account Controller**: Temporal account operations
- **Address Controller**: Address temporal operations
- **Decision Controller**: Decision workflow management
- **Demand Controller**: Service demand management
- **Notification Controller**: Temporal notifications
- **Order Controller**: Temporal order processing
- **SLOF Controller**: Service Level Objective Framework
- **User Task Controller**: User task management
- Database tables: `mail_notification`, `temporal_decision`, `temporal_user_task`

### 13. User Module (`src/modules/user/`)
- **Billing Account Controller**: Customer billing information
- **Easyfix Controller**: Technical support for users
- **KCI Controller**: Keep Customers Informed billing notifications
- **Linked Account Controller**: Account linking services
- **Pay For Anyone Controller**: Third-party payment services
- **Profile Account Controller**: User profile management
- **SME Account Controller**: Small and Medium Enterprise accounts
- Database tables: `account_settings`, `linked_accounts`

### 14. Util Module (`src/modules/util/`)
- **Alert Controller**: System alerts and notifications
- **Config Controller**: Configuration management
- **LOV Controller**: List of Values management
- Database tables: Various LOV tables for addresses, alerts, complaints, etc.

## Middleware

### 1. Authentication Middleware (`src/middleware/auth.ts`)
- **validateToken**: Bearer token validation
- **validateApiKey**: API key validation
- **restrictToInternal**: Internal system access control

### 2. Error Middleware (`src/middleware/error.ts`)
- Custom error handling with UE_ERROR class
- Standardized error responses

### 3. UAID Middleware (`src/middleware/uaid/`)
Universal Authentication and Identity Directory
- **Identity**: User identity management
- **Profile**: User profile services
- **Session**: Session management and validation
- **SME**: Small and Medium Enterprise identity
- **TAC**: Terms and Conditions management

## Integration Layer (`src/integration/`)

### 1. BIZ Integration (`src/integration/biz/`)
- **Cloud Connect**: Business cloud connectivity
- **eCommerce**: E-commerce platform integration

### 2. Email Template Integration (`src/integration/emailTemplate/`)
- **Autopay Templates**: Automatic payment email templates
- **OSES Templates**: Payment service email templates
- **OTT Email Templates**: Streaming service notifications

### 3. OMG Integration (`src/integration/omg/`)
Over-the-top Media Gateway
- Netflix activation, plan changes, account recovery
- Disney+ mobile number changes
- OTT subscription management
- OTT entitlement verification

### 4. Temporal Integration (`src/integration/temporal/`)
- Workflow and temporal operations integration

### 5. WERAS Integration (`src/integration/weras/`)
Wireless Enterprise Resource and Analytics System
- Customer bills retrieval
- Membership information
- Rewards and promotions
- Transaction history

### 6. WSO2 Integration (`src/integration/wso2/`)
Enterprise Service Bus integration
- **Address**: Granite address services
- **Eligibility**: Customer eligibility checks
- **Notification**: SMS and email services
- **Order**: Order management and tracking
- **Payment**: Payment processing
- **Record**: Customer records and service requests
- **User**: Customer account management

## Configuration

### Environment Configuration (`src/config/env.config.ts`)
Comprehensive environment variable management for:
- Database connections
- WSO2 API endpoints and tokens
- External service URLs
- Authentication keys
- Email service configurations

### Database Configuration (`src/config/db.config.ts`)
- PostgreSQL connection pooling
- SSL configuration for production
- Connection lifecycle management

### Cache Configuration (`src/config/cache.config.ts`)
- Redis connection setup
- Caching strategies

### Logging Configuration (`src/config/pinoLog.config.ts`)
- Structured logging with Pino
- Environment-specific log levels

## API Security Levels

### 1. Private API (🔒)
- Requires BOTH Bearer Token AND API Key
- Used for highly sensitive operations
- Full authentication and authorization

### 2. Protected API (🔐)
- Requires API Key only
- Used for trusted integrations
- Suitable for server-to-server communication

### 3. Public API (🌐)
- No authentication required
- Limited to non-sensitive operations
- Rate limiting applied

### 4. Internal API (🛠️)
- Restricted to internal systems
- IP-based access control
- Developer and system administration use

## Database Schema Highlights

### Key Tables
- `addons_catalogue`: Product catalog for add-on services
- `addons_metadata`: Metadata for add-on categories
- `tv_pack_catalogue`: Television package information
- `ott_plan_catalogue`: Streaming service plans
- `bank_list`: Supported banks for payments
- `account_settings`: User account preferences
- `linked_accounts`: Account linking information
- `orderable`: Product ordering information
- `ott_order`: Streaming service orders
- `rewards_template`: Loyalty program templates

## Development Commands

```bash
# Development
bun dev              # Start development server
bun test             # Run tests
bun lint             # Run linter

# Database
bun db:generate      # Generate SQL schema
bun db:up            # Run migrations
bun db:studio        # Start Drizzle Studio

# Utilities
bun trace            # Generate TypeScript trace
bun cleanup          # Clean and update dependencies
```

## Key Features

1. **Comprehensive API Coverage**: Handles telecommunications, billing, orders, payments, and customer management
2. **Multi-level Authentication**: Flexible security model for different use cases
3. **Extensive Integration**: Connects with multiple backend systems (WSO2, WERAS, OMG, etc.)
4. **Real-time Monitoring**: OpenTelemetry integration for observability
5. **Scalable Architecture**: Modular design with clear separation of concerns
6. **Type Safety**: Full TypeScript implementation with strict typing
7. **Modern Stack**: Uses the latest technologies (Bun, ElysiaJS, DrizzleORM)
8. **Docker Support**: Containerized deployment with Docker Compose
9. **CI/CD Ready**: GitHub Actions integration for automated deployment
10. **Comprehensive Documentation**: Scalar/Swagger API documentation

This Universal Engine serves as the backbone for Unifi's customer experience platform, providing robust, scalable, and secure API services for telecommunication operations.

## Code Examples and Implementation Details

### Main Application Entry Point (`src/index.ts`)
```TypeScript
import { Elysia } from 'elysia';
import { opentelemetry } from '@elysiajs/opentelemetry';
import { cors } from '@elysiajs/cors';
import swagger from '@elysiajs/swagger';

const app = new Elysia({
    name: '@grotto/logging',
    serve: { idleTimeout: 255 }
});

// OpenTelemetry tracing
app.use(opentelemetry({
    spanProcessors: [
        new BatchSpanProcessor(
            new OTLPTraceExporter({
                url: 'https://api.axiom.co/v1/traces',
                headers: {
                    Authorization: `Bearer ${process.env.AXIOM_TOKEN}`,
                    'X-Axiom-Dataset': `${process.env.AXIOM_DATASET}`
                }
            })
        )
    ]
}));

// CORS configuration
app.use(cors({
    origin: true,
    credentials: true
}));

// API documentation
app.use(swagger({
    documentation: {
        info: {
            title: 'Universal Engine API,'
            version: '0.0.1,'
            description: 'Unifi eXperience Engine Backend Service'
        }
    }
}));
```

### Authentication Implementation
```TypeScript
// Bearer Token Validation
export const validateToken = async (authValue: string): Promise<void> => {
    if (!authValue.startsWith('Bearer')) {
        throw new UE_ERROR('Invalid token! You are not authorized!',
            StatusCodeEnum.UNAUTHORIZED_ERROR
        );
    }

    const match = authValue.match(/^Bearer\s+(\S+)$/i);
    if (!match) {
        throw new UE_ERROR('Invalid token! You are not authorized!',
            StatusCodeEnum.UNAUTHORIZED_ERROR
        );
    }

    await getIdTokenInfo(match[1]);
};

// API Key Validation
export const validateApiKey = (xApiKey: string) => {
    const apiKey = process.env.X_API_KEY;
    if (xApiKey !== apiKey) {
        throw new UE_ERROR('Invalid key! You are not authorized!',
            StatusCodeEnum.UNAUTHORIZED_ERROR
        );
    }
};
```

### Database Schema Examples
```typescript
// Add-ons Catalogue Table
export const addonsCatalogueTableSchema = pgTable('addons_catalogue', {
    Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
    Name: text('name').unique().notNull(),
    DisplayName: text('display_name').notNull(),
    Category: text('category').notNull(),
    Tag: text('tag').notNull(),
    Description: json('description').$type<DeviceDescription>(),
    Summary: text('summary').notNull(),
    Specification: text('specification'),
    ImageUrl: text('image_url').notNull(),
    MonthlyCommitment: doublePrecision('monthly_commitment').notNull(),
    DiscountPercentage: doublePrecision('discount_percentage').notNull(),
    RRP: doublePrecision('recommended_retail_price'),
    ContractTerm: integer('contract_term').notNull(),
    PartNumber: text('part_number'),
    ProductId: text('product_id'),
    CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
    UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

// Bank List Table
export const bankListTableSchema = pgTable('bank_list', {
    Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
    BankName: text('bank_name').notNull(),
    BankCode: integer('bank_code').notNull(),
    Flag: json('flag').$type<string[]>().notNull(),
    CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
    UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});
```

### Controller Implementation Example
```typescript
// Add-ons Catalogue Controller
const addOnsV1Routes = new Elysia()
    .use(bearer())
    .resolve(async ctx => {
        const idTokenInfo = await getIdTokenInfo(ctx.bearer);
        return {
            AddOnsCatalogue: new AddOnsCatalogue(randomUUID(), idTokenInfo)
        };
    })
    .get(
        '/add-ons',
        async (ctx): Promise<AddOnsCatalogueRes> => {
            return await ctx.AddOnsCatalogue.getAddOnsCatalogue(ctx.query);
        },
        {
            detail: {
                description: 'Retrieve a list of add-ons catalogue for Smart Device, Smart Home, Mesh Wi-Fi, UPB, TV Pack, and Blacknut.',
                tags: ['Catalogue']
            },
            query: addonsCatalogueReqSchema,
            response: {
                200: addonsCatalogueResSchema,
                500: errorBaseResponseSchema
            }
        }
    );
```

### Route Guards and Security
```TypeScript
// Private API Routes (Bearer Token + API Key)
apiModuleRoutes.guard(
    {
        headers: baseHeaderSchema,
        beforeHandle: async ({
            headers: { authorization, 'x-api-key': xApiKey }
        }) => {
            await validateToken(authorization);
            validateApiKey(xApiKey);
        }
    },
    app => app
        .use(privateAddressV1Routes)
        .use(privateCatalogueV1Routes)
        .use(privateOrderV1Routes)
        // ... other private routes
);

// Protected API Routes (API Key only)
apiModuleRoutes.guard(
    {
        headers: t.Object({
            'x-api-key': t.String(),
            source: t.Enum(SourceEnum),
            segment: t.String()
        }),
        beforeHandle({ headers: { 'x-api-key': xApiKey } }) {
            validateApiKey(xApiKey);
        }
    },
    app => app
        .use(protectedUserV1Routes)
        .use(protectedPaymentV1Routes)
        // ... other protected routes
);
```

### Integration Layer Example
```typescript
// WSO2 Integration
export default class Wso2OrderIntegration {
    private requestId: string;

    constructor(requestId: string) {
        this.requestId = requestId;
    }

    async checkStock(request: Wso2CheckStockReq): Promise<Wso2CheckStockRes> {
        const url = envConfig().WSO2_CHECK_STOCK;
        const token = await getApimToken(this.requestId);

        const response = await fetchApi<Wso2CheckStockRes>({
            url,
            method: 'POST,'
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: request,
            requestId: this.requestId
        });

        return response;
    }
}
```

### Error Handling
```TypeScript
export class UE_ERROR extends Error {
    public statusCode: number;

    constructor (message: string, statusCode: number = 500) {
        super(message);
        this.name = 'UE_ERROR';
        this.statusCode = statusCode;
    }
}

// Usage in controllers
if (!validationResult) {
    throw new UE_ERROR('Validation failed,'
        StatusCodeEnum.BAD_REQUEST_ERROR
    );
}
```

### Environment Configuration Structure
```typescript
const envSchema = t.Object({
    SERVER_PREFIX_PATH: t.String(),
    WSO2_BASIC_AUTH: t.String(),
    WSO2_APIM_HOURLY_TOKEN: t.String(),
    WSO2_CHECK_STOCK: t.String(),
    WSO2_ORDER_SUBMIT: t.String(),
    DB_HOST: t.String(),
    DB_PORT: t.String(),
    DB_NAME: t.String(),
    DB_USER: t.String(),
    DB_PASSWORD: t.String(),
    REDIS_HOST: t.String(),
    REDIS_PORT: t.String(),
    X_API_KEY: t.String(),
    INTERNAL_API_KEY: t.String(),
    // ... 100+ more environment variables
});
```

## Package.json Dependencies
```json
{
    "dependencies": {
        "@elysiajs/bearer": "^1.3.0",
        "@elysiajs/cors": "^1.3.1",
        "@elysiajs/jwt": "^1.3.0",
        "@elysiajs/opentelemetry": "1.2.0",
        "@elysiajs/swagger": "^1.3.0",
        "drizzle-orm": "^0.43.1",
        "elysia": "^1.3.1",
        "ioredis": "^5.6.1",
        "pg": "^8.15.6",
        "typescript": "^5.8.3"
    }
}
```

## Docker Configuration
```dockerfile
FROM oven/bun:latest
WORKDIR /app
COPY package.json bun.lock ./
RUN bun install
COPY . .
EXPOSE 3000
CMD ["bun", "src/index.ts"]
```

This comprehensive documentation provides complete context for understanding and working with the Universal Engine codebase.
