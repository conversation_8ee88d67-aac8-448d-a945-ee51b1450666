---
prev:
  text: 'Collections'
  link: './collections'

next:
  text: 'Development Rules'
  link: './development-rules.md'
---
[⬅️ **Main**](../index.md)

# Access Controls

Access control is a neat feature that determines what user can and cannot do or see in Universal Ops. This feature is scoped down to the CRUD operations and executed before 2 things:

1. Before changes are made
2. Before operations are completed

## Configuring Roles

PayloadCMS has made access controls easy by implementing role-based access controls **(RBAC)** for all of their collections and globals.

### Create, Update or Delete Roles

1. To create roles, developers need to add the roles in options attribute of user collection's select UI. This file can be found under `collections` -> `Users` -> `index.ts`
   ![Add Roles](../img/universal-ops/access-controls/add_roles.png)
2. In the same attribute, role's label and value can be updated or delete the whole role object entirely.
   a. `label` specifies the role's usage intentions and `value` will be set into the database and used for checking the user before allowing them the operations.
   b. The value of `value` attribute must be in **lowercase**.
3. After making changes to the attribute, developers must run 3 commands to update the database and ensures PayloadCMS able to use the changes. These 3 commands are:
   a. `generate:types`: To update PayloadCMS' types and ensures type safety is upheld
   b. `generate:schema`: To update PayloadCMS' Drizzle schema. The role options are turned into enum that will be stored in the database.
   c. `generate:sql`: To generate a SQL file that contains the updated changes that need to be done on the database side such as to add the latest role into the enum.

### Using Roles

- **Set roles to collections:** Developers need to import the `hasAnyRole` function into the access attribute of the collection.

- **Assign roles to users:** An admin can assign the suitable role to the user. The steps for this action are as below:
  1. Log into your Universal Ops account and click on the Users card.
     ![Users](../img/universal-ops/access-controls/users.png)
  2. Find the user that needed the role to be set and click on their email to open the editor.
     ![Select User](../img/universal-ops/access-controls/select_user.png)
     ![Editor](../img/universal-ops/access-controls/editor.png)
  3. In the editor, there is a dropdown for roles. Select the suitable one and save the changes.
     ![Dropdown](../img/universal-ops/access-controls/roles_dropdown.png)
     ![Save](../img/universal-ops/access-controls/save.png)

> [!NOTE]💡 Pro Tip
> Users can have more than one role if needed. However, this is highly discouraged. Admins also can change user's role as they see fit.

## Current Roles

These are the roles and their capabilities that are currently set in the application:

- **Admin**
  - For Metadata team
  - Can view and edit all UE tables
  - Can view and edit all forms
- **Support**
  - For Operation team
  - Can view all UE tables
  - Can edit some UE tables
  - Can view and edit all forms
- **Catalogue**
  - For OMG Team
  - Can view and edit Addons tables
- **Network**
  - For NOC team
  - Can view and edit to Unifi Alert tables only
- **Marketing**
  - For Marketing team and Product Owners (PO)
  - Can view and edit pages
  - Can view and edit forms
- **Developer**
  - For developers
  - Can view non-PII tables
  - Can view and edit forms
