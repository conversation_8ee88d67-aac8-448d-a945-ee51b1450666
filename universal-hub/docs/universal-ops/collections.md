---
prev:
  text: 'Installation & Setup'
  link: './installation-setup'

next:
  text: 'Access Control'
  link: './access-controls.md'
---



[⬅️ **Main**](../index.md)

# ⚙️ Collections

## 1. What is a Collection?

A **Collection** is a group of records, called Documents, that all share a common schema. You can define as many Collections as your application needs. In the Universal Ops, each document in a Collection is stored in the Database based on the Fields that you define, and automatically generates a REST API and GraphQL API.

Collections are also used to achieve Authentication. By defining a Collection with auth options, that Collection receives additional operations to support user authentication.

Collections are the primary way to structure recurring data and manage content of users, pages, posts. In the Universal Ops, the collection also will manage the data of App Web Config, Payment, Catalogue, Order, and Unifi Alerts.  Each Collection can have its own unique Access Control, Hooks, Admin Options, and more.

## 2. Config Options

In the Universal App, Collections are defined in `collections/index.ts` and then imported into the main Payload Config.

Here the Collection Config might look like:

![Collection Config](../img/universal-ops/collections/collection-config.png)

---

Below are the Admin options we commonly use to manage and customize the Universal Ops CMS interface. These settings help define how collections appear in the admin panel and enhance usability for content editors and administrators.

| Options          |    Description                                                                   
| ---------------- | ----------------------------------------------------------------------------------------------------------- |
| Admin            | The behavior of Collections within the Admin Panel can be fully customized to fit the needs of your application. This includes grouping or hiding their navigation links, adding Custom Components, selecting which fields to display in the List View, and more. <a href="https://payloadcms.com/docs/configuration/collections#admin-options">more details</a>                                                                                                                               |
| Slug             | Unique, URL-friendly string that will act as an identifier for this Collection. This property are required for each collections.               | 
| dbName           | Custom table or Collection name depending on the Postgres Database. Auto-generated from slug if not defined.                                   | 
| fields           | The property is required for every Collections. This will determine the structure and functionality of the data stored within the Collection. The name must be same as fieldname in the database.<a href="https://payloadcms.com/docs/fields/overview"> More details. </a>                                                                                                    |
| labels           | Singular and plural labels for use in identifying this Collection throughout Payload. Auto-generated from dbName or Slug if not defined.                                                                      | 
| timestamps       | Set to false to disable documents' automatically generated createdAt and updatedAt timestamps                                                                                                                       |
| hooks            | Entry point for Hooks. <a href="https://payloadcms.com/docs/hooks/overview#collection-hooks"> More details.</a>                                                                                                                               |


## 3. Getting Started: Required Setup Commands
Before running the application, make sure to execute the following commands to generate essential files and ensure everything is properly configured:

### 🧱 Generate Types
```bash
npm run generate:types
```
> Automatically generates TypeScript types based on the Payload collection configurations. See ``collections/payload-types.ts`` for the generated output.

### 🧱 Generate Schema
```bash
npm run generate:schema
```
>Executes a command that generates a human-readable schema file—typically in JSON or Markdown format—for debugging, documentation, and validation purposes. The resulting schema can be found in ``collections/payload-generated-schema.ts``

### 🧱 Generate Sql (If applicable)
```bash
npm run generate:sql
```
>Generates an SQL schema based on Universal Ops collections. This is useful for syncing with Universal App database. The output file will be created in the drizzle folder.

## 4. List of Collections

| Collection              |Description                                                                                          |
| ------------------------| --------------------------------------------------------------------------------------------------- |
| BillPaymentTxnHistory   | Tracks and manage customer's payment and status                                                                                          | 
| OsesTxnHistory          | Tracking of payment-related activities such as transaction data, payment method, statuses, and related order                             | 
| NonOrderableTxnHistory  |                                                                                                                                          | 
| OrderableTxnHistory     | Tracks and manage customer order and status                                                                                              |  
| CustomerOrder           | Tracks and manage customer purchases and status                                                                                          | 
| AddonsCatalogue         | Stores and manage add-ons details and pricing                                                                                            | 
| TvPackCatalogue         | Stores and manage TV Pack details and pricing                                                                                            |
| OrderablePlan           | Track and manage offering plan details                                                                                                   | 
| MaintenancePage         | Manages content related to service availability, downtime notices, and maintenance actions across platforms (web or app)                                                                                                                                                                 | 
| ForceUpdate             | Manages version enforcement policies across mobile or web platforms. It is typically used to control which app versions are supported and whether users are required to update.                                       | 
| UnifiAlertTitle         | Manages the titles or headings used for Unifi alerts. These titles are typically short, user-facing messages that summarize the nature of the alert and are often displayed at the top of alert notifications                                                                                                  |
| UnifiAlertService       | Manages the services or components related to an alert                                                                                   | 
| UnifiAlertArea          | Manages the specific areas, zones, or segments where Unifi alerts apply or should be displayed                                           | 
| UnifiAlertCause         | Manages the possible causes or reasons behind Unifi alerts                                                                               |
| UnifiAlertStatus        | Manages the various statuses an alert can have within the Unifi alert. These statuses help track the lifecycle and resolution state of each alert                                                                                                                                                                | 

---




