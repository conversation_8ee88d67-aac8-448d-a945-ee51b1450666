---
next:
  text: 'Installation Setup'
  link: './installation-setup.md'
---

[⬅️ **Main**](../index.md)

# 🌍 Universal Ops

Welcome to Unifi Digital's Operation and Support Website! This application is created to replace these applications:

- OASIS
- Camunda
- Drupal
- Ops Console

You can access the website from these URLs:
| Environment | URL |
|-------------|----------------------|
| local | localhost:3000/admin |
| SIT | [URL] |
| Preprod | [URL] |
| Prod | [URL] |

_Kindly contact the PIC for username and password._

## Payload CMS

This application is created using [Payload CMS](https://payloadcms.com/docs/getting-started/what-is-payload) framework. It is a Next.js framework . Before we jump into the technical stuff, we need to understand a few concepts that Payload CMS used. Here's a table to help you understand these concepts and the equivalent in Universal Engine

| Payload Concepts | Description                                                                                                                                    | UE Concepts                      |
| ---------------- | ---------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------- |
| Config           | A deep configuration of the Payload application. It is in the form of a fully-typed JSON object                                                |                                  |
| Database         | A place where all data are stored. Payload accepts most common databases and stored its own data in the same schema with the existing database | Database                         |
| Collections      | A group of records that shared the same schema                                                                                                 | Entity/Table                     |
| Document         | A single record of data                                                                                                                        | Row                              |
| Fields           | Schema that defines the collection. It is the building blocks of Payload                                                                       | Columns                          |
| Access Control   | Determines what a user can and cannot do in the application                                                                                    | Role-Based Access Control (RBAC) |
| Admin Panel      | A website to manage users, tables, media and pages                                                                                             | Admin Portal                     |

> [!NOTE]💡 Note
> More details can be found in [Payload's documentation](https://payloadcms.com/docs/getting-started/concepts)
