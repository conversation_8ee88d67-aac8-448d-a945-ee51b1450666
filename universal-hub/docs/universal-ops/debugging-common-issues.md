---
prev:
    text: "Environment Variable"
    link: "./environment-variables.md"

next: false
---
# 🔧 Debugging Common Issues

## Issue 1: Database Table Naming Convention

Payload CMS fails to recognize or interact correctly with tables named using camelCase or PascalCase in existing PostgreSQL databases.

#### Cause:

- Payload CMS automatically converts collection and global slugs to snake_case for table names.
- PostgreSQL treats unquoted identifiers as lowercase, but camelCase and PascalCase are case-sensitive and require quoting.
- Payload does not quote table names in queries, which causes it to fail when encountering mixed-case table names.


#### Solution:

Rename all tables in existing database to use snake_case.

---  

## Issue 2: Relationship Field Requires Standard id Column


Payload CMS relationships do not work correctly when the related table uses a custom primary key field instead of the default id column.

#### Cause:  

- Payload expects a standard id field (type: text or UUID) as the primary key when resolving relationships between collections.
- If the related collection uses a custom primary key (e.g., user_id, uuid, customId), Payload cannot properly fetch or resolve relationship data.
- Payload does not currently support configuring the reference field used in relationships.

#### Solution:  

- Ensure all related collections use a standard id field as the primary key.
- Avoid using custom IDs as primary keys in collections that are referenced by relationships.