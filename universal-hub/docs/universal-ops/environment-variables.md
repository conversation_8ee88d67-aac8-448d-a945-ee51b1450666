---
prev:
  text: "Project Structure"
  link: "./project-structure.md"
next:
  text: "Debugging Common Issues"
  link: "./debugging-common-issues.md"
---

[⬅️ **Main**](../index.md)

# 🌱 Environment Variables

Welcome to the secret sauce of the project—**environment variables**! These little gems keep all our sensitive info, like API keys and database passwords, safe and configurable across environments.

## 🔑 Key Environment Variables

Here's a rundown of the main environment variables you'll be working with:

- **`DATABASE_URI:`** Specifies the connection string for the PostgreSQL database.
- **`PAYLOAD_SECRET:`** A secret string used by Payload CMS to sign and verify JWT tokens.
- **`NEXT_PUBLIC_SERVER_URL:`** The base URL of the application frontend or API server. Used by the frontend to construct links and perform CORS checks.(e.g:http://localhost:3000)
- **`MINIO_ROOT_USER:`** Admin username for <PERSON><PERSON>.
- **`MINIO_ROOT_PASSWORD:`** Admin password for <PERSON><PERSON>.
- **`S3_ENDPOINT:`** URL to access the MinIO service.
- **`S3_ACCESS_KEY_ID:`** Access key ID used by Payload to authenticate with MinIO.
- **`S3_SECRET_ACCESS_KEY:`** Secret access key for MinIO authentication.
- **`S3_BUCKET:`** The bucket name to use for storing files.

