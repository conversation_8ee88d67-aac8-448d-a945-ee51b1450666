---
prev:
  text: 'Concepts'
  link: './concepts'

next:
  text: 'Collections'
  link: './collections.md'
---

[⬅️ **Main**](../index.md)

# ⚙️ Installation & Setup

## 🔧 Tech Stack

- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **Framework:** [Payload CMS](https://payloadcms.com/docs/getting-started/what-is-payload)
- **Runtime:** [PNPM](https://pnpm.io/installation)
- **ORM:** [Drizzle ORM](https://orm.drizzle.team/)
- **Database:** [Postgres DB](https://www.postgresql.org/)
- **Object Storage (For Image and Videos):** [MinIO](https://min.io/docs/minio/kubernetes/upstream/index.html)
- **Containerization:** [Docker](https://www.docker.com/)
- **Docs:** [VitePress](https://vitepress.dev/)

## 🚀 Running the Application

Welcome aboard! Getting your application up and running is a breeze with Docker! Here's your step-by-step guide:

### 2. Install Dependencies

Install the required dependencies using the following command:

```bash
pnpm install
```

### 3. Start Universal Engine's Container

Before build and run the application, you need to start up the Universal Engine's containers first. Most of the steps can be found [here](../universal-engine/getting-started/installation-setup.md).

However, since Payload insist on using the port 3000 for local development, we have to change elysia-app's application port number from 3000 to 3001 in Universal Engine's `docker-compose.yaml` file like below:
![Change Port Number](../img/universal-ops/installation-setup/change_port_number.png)

After the change has been made, you can start the container.

### 4. Build and Run in Development Mode

To build and run the app in development mode (in the background), use this command:

```bash
docker-compose up -d
```

Once all the services are created, go to Docker and open the Payload logs by double-clicking on its service and wait until it has stated that it is ready like in the image below:
![Docker Service](../img/universal-ops/installation-setup/docker_service.png)
![Docker Ready](../img/universal-ops/installation-setup/docker_ready.png)

After that, you can go to localhost:3000/admin. When it is successful, it will show this page:
![Payload App](../img/universal-ops/installation-setup/payload_app.png)

### 5. Restart the Application (if you have made changes)

```bash
docker-compose restart
```

### 6. Restart specific service (if you have made changes)

```bash
docker-compose restart <service name>
```

For example, if you want to restart the `payload` service, run:

```bash
docker-compose restart payload
```

### 7. View Configuration for Debugging

Need to see the full configuration for debugging? Run:

```bash
docker-compose config
```

### 8. View Application Logs

To monitor the logs in real-time using terminal, use this command:

```bash
docker-compose logs -f
```

Your Payload App should log outputs like this:

![Payload App Logs](../img/universal-ops/installation-setup/docker_logs.png)

> [!NOTE] 💡 Pro Tip
> You can also view the logs through Docker Desktop! Head to the 'Containers' tab, select your container, and hit the 'Logs' tab for a live feed.

### 9. Stopping the Application

When you're ready to stop the app, simply run:

```bash
docker-compose down
```
