---
prev:
    text: "Development Rules"
    link: "./development-rules.md"

next:
    text: "Environment Variables"
    link: "./environment-variables.md"

---

[⬅️ **Main**](../index.md)

# 🏗️ Project Structure

This document outlines the folder structure and purpose of each main directory in the Universal Ops.

## 🌱 Root Directory

The `root` directory contains all the essential configurations for the project. Here are the key folders and files:

- `.github` — stores CI/CD pipeline configuration.
- `.husky` — contains check automation settings for pre-commit hooks.
- `drizzle.config` — configuration files used to connect to the PostgreSQL database.
- `migration` — keeps SQL files and snapshots generated by <PERSON><PERSON><PERSON>, useful for database migrations.
- `node_modules` - contains all the installed dependencies (libraries, tools, frameworks, etc.)
- `src` — holds all the source code (details coming up next!).
- `package.json` — lists all the project dependencies and scripts.

## 🗂️ SRC Directory

The `src` directory is where the main application code is located. Below are the key folders:

* `access/` - Defines access control logic for collections or globals in Payload CMS.
* `app/` - Contains application-specific code.
* `blocks/` - Reusable content blocks for flexible page/content structures.
* `collections/` - Defines the main Payload CMS collections.
    * js/, Pages/, Posts/, Unifi-Digital/, Users/:
        Each folder likely contains schema definitions for these collections.
    * Categories.ts, Media.ts, index.ts:
        Collection schema files.
* `components/` - UI components for use across the front-end or admin UI.
* `endpoints/` - Custom REST or GraphQL endpoints extending Payload's API.
* `fields/` - Custom or shared fields used across collection schemas.
* `hooks/` – Defines global or reusable lifecycle hooks applied across the application (e.g., logging, setting update timestamps).

Naming convention:

- All folder names are lowercase.
- Two file types: `const` files uses Camel Case, while `class` files uses Pascal Case.

## ⚙️ Collections

This directory houses all of the content and data collections used in the Universal Ops. Each file or subdirectory represents a distinct collection schema or grouping of related collections. Collections define how Universal Ops stores, structures, and retrieves content. All custom collections specific to Universal Ops are organized within the **Unifi-Digital** folder. The subfolders within Unifi-Digital represent these custom groupings:

🧩 **AppWebConfig.ts:** App-wide web configuration schema.
   - `ForceUpdate.ts:` Enforces client updates (e.g., version checks).
   - `MaintenancePage.ts:` Schema to manage maintenance mode content.

📚 **Catalogue/** – Product & Subscription Management
- This folder groups all catalogue-related content and product offerings:
    - `AddOnsCatalogue.ts:` Defines add-on packages users can subscribe to.
    - `AddonsMetadata.ts:` Metadata associated with add-ons.
    - `CampaignCms.ts:` CMS for promotional campaigns.
    - `OrderablePlan.ts:` Base subscription or product plans.
    - `OttCatalogue.ts:` Over-the-top media content offerings.
    - `TvPackCatalogue.ts:` Television package configurations.

📊 **Lov/** – List of Values
- Used for enums, dropdowns, and static reference data.
    - `AddressLov.ts:` Predefined address list.
    - `TnpsRateLov.ts:` TNPS rating options.

🛒 **Order/** – Order Management
- Handles user orders and related transaction tracking.
    - `CustomerOrder.ts:` Main customer order collection.
    - `OrderTxnHistory.ts:` Transaction logs related to orders.
    - `NonOrderTxnHistory.ts:` Logs for transactions outside formal orders.
    - `OttOrder.ts:` OTT-specific order data.

💳 **Payment/** – Payment Records
- Track billing and payment-related transactions.
    - `BillPaymentTxnHistory.ts:` History of bill payment activities.
    - `OsesTxnHistory.ts:` Payment logs from operating system platforms (e.g., iOS, Android).

⏱️ **Temporal/** – Workflow & Task Automation
- Define and track user-driven or system-automated workflows.
    - `TemporalUserTask.ts:` Individual user task instance.
    - `TemporalUserTaskDefinition.ts:` Templates for user tasks.
    - `TemporalDecision.ts:` Decision points in a workflow.

🚨 **UnifiAlerts/** – System Alerts & Notifications
- Manages alert messages, statuses, and configurations.
    - `AlertArea.ts:` Areas or scopes where alerts apply.
    - `AlertCause.ts:` Root causes of alerts.
    - `AlertService.ts:` Services affected by alerts.
    - `AlertStatus.ts:` Current alert statuses (e.g., resolved, active).
    - `AlertTitle.ts:` Alert title presets.
    - `UnifiAlert.ts:` Main alert content and metadata.

> Notes: In Universal Ops, some collection are structured with its own local **hooks/** folder. This approach keeps hook logic modular, maintainable, and tightly coupled with its respective collection.

## 🛠️ Configuration Files

In our Universal Ops project, we utilizes the following key configuration files to define our data structures and administrative interface:

### 🧱 `payload-generated-schema.ts`

This file is **automatically generated** by Payload CMS based on `payload.config.ts`. Think of it as the **blueprint of data models** in TypeScript.

* **Purpose:** It contains the TypeScript interfaces and types that represent our collections (like users, products, articles, etc.) and globals. This ensures type safety throughout our codebase when interacting with the data.

* **Key Contents:**
    * TypeScript interfaces defining the shape of each collection's document, including all its fields and their respective types.
    * Utility types for querying and manipulating data.
    * Type definitions for global configurations.

* **How it's used:** When we fetch data from Payload, we can leverage the types defined in this file to ensure we're working with the correct data structure, catching potential type errors during development. For example, if we have a `Order` collection, this file will define the `Order` interface with all its properties (name, description, price, etc.).

* **Important Note:** We should **not manually edit** this file. Any changes we need to make to our data structure should be done within `payload.config.ts`, which will then regenerate this schema file.

### 🧱 `payload-types.ts`

Similar to `payload-generated-schema.ts`, this file also contains TypeScript definitions related to project setup. However, it often includes **more utility types and potentially some manually added custom types** that might be helpful across Universal Ops

* **Purpose:** To provide a broader set of TypeScript types and interfaces for interacting with Payload, potentially including custom types we might define for specific functionalities within Universal Ops.

* **Key Contents:**
    * A superset of the types found in `payload-generated-schema.ts`.
    * Utility types for API responses, authentication, and more.
    * Potentially, custom TypeScript types or enums specific to the Universal Ops that you might have added manually (though this is less common for purely generated files).

* **How it's used:** This file serves as a central location for TypeScript type definitions, making it easier to maintain type consistency across our frontend and backend when dealing with Payload data and API interactions within Universal Ops.

### 🧱 `payload.config.ts`

This is the **heart of your Universal Ops configuration**. It's where we **define the structure and behavior of the system**.

* **Purpose:** To configure all aspects of Payload CMS instance, including:
    * Defining  **collections** (the different types of content we'll manage).
    * Defining **globals** (single instances of data, like site settings).
    * Configuring **fields** within our collections and globals (the individual pieces of data).
    * Setting up **access control** rules (who can create, read, update, and delete content).
    * Integrating **plugins** to extend Payload's functionality.
    * Configuring **localization**, **hooks**, and other advanced features.

* **Key Contents (relevant to Universal Ops):**
    * An array of `CollectionConfig` objects, each defining a collection with its fields, access control, and other options. 
    * An array of `GlobalConfig` objects, defining your global settings.
    * Potentially, configurations for plugins, hooks, and other features used in Universal Ops.

* **How it's used:** This file dictates how content is structured and managed within Universal Ops backend. Any changes to your data models or admin interface are made here. Payload then uses this configuration to generate the database schema, the admin UI, and the TypeScript types in `payload-generated-schema.ts` and `payload-types.ts`.

>By understanding the role of these three files, you'll have a solid foundation for developing and maintaining the content management aspects of Universal Ops. Remember that `payload.config.ts` is where you make your structural definitions, and the other two are largely generated outputs that ensure type safety in your application.

