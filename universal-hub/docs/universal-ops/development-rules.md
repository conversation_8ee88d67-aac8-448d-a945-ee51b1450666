---
prev:
  text: 'Access Controls'
  link: './access-controls'

next:
  text: 'Project Structure'
  link: './project-structure'
---

[⬅️ **Main**](../index.md)

# 📜 Development Rules

This document outlines the specific development rules and guidelines that must be followed when contributing to the development of Universal Ops. This system utilizes Payload CMS as its content management framework, PostgreSQL as its primary database, and pnpm as its package manager and build tool. Adhering to these guidelines will ensure code consistency, maintainability, and a smooth collaborative development process. The following rules and guidelines must be followed by all team members.


## 1. Project Setup & Environment

### 1.1 Repository Management:

All code changes must be managed through Git using the established branching strategy. In Universal Ops we have **Main Branch** as core branch and using supporting branches such as **feature/xx** and **release/xx** as defined by the Gitflow workflow. This ensures proper version control, facilitates collaboration, and allows for organized feature development and bug fixing.

Guidelines:
- Create feature branches for new functionalities.
- Submit pull requests for code review before merging into the main branch.
- Write clear and concise commit messages.


### 1.2 Local Development Environment:

Developers must set up their local development environment according to the provided setup guide (<a href=./installation-setup.html>installation-setup</a>).A consistent development environment minimizes "it works on my machine" issues.Follow the step-by-step instructions in the setup guide to configure your environment, including database setup and environment variable configuration.

### 1.3 Dependency Management:

All project dependencies must be managed using pnpm. Do not manually modify **node_modules**. pnpm's efficient dependency management (using hard links and symlinks) saves disk space and can lead to faster installations. It also helps prevent issues with inconsistent dependency versions.

Guideline:
- Use pnpm add <**package-name**> for adding production dependencies.
- Use pnpm add -D <**package-name**> for adding development dependencies.
- Run pnpm install after pulling changes from the repository.

## 2. Payload CMS Development

### 2.1 Collection & Global Design:

All new collections and globals must be designed following the established data model and naming conventions. Our data model follows the Universal Engine database schema. If there any changes with the Universal Ops schema, you need to inform the Universal Engine squads as well. A consistent data model ensures clarity and maintainability of our content structure.

Guideline:
- Use PascalCase for collection and global API IDs (e.g., CustomerOrder, AddOnsCatalogue).
- Use snakecase with underscore for field names (e.g., plan_id, tv_pack_name).
- Provide clear and descriptive labels for all collections, globals, and fields in the admin UI.
- Document the purpose and usage of each collection and field in the data model documentation.
- Provide clear remark for the code function.

### 2.2 Field Usage & Configuration:

Utilize Payload's built-in field types appropriately. Justify the use of custom fields or complex configurations in code comments.

Guideline:
- Implement appropriate validation rules for all fields to ensure data integrity.
- Utilize slug fields with the unique: true property for SEO-friendly URLs where applicable. Document any custom validate functions thoroughly.

### 2.3 Hook Implementation:

Custom business logic should primarily be implemented using Payload's hook system. Keep hook functions concise and focused on a single responsibility.Hooks provide a structured way to extend Payload's functionality without modifying core files.

Guidance:
- Place hook functions within a dedicated hooks directory (e.g., payload/hooks).
- Document the purpose and functionality of each hook clearly.
- Avoid complex, multi-step logic within a single hook; break it down into reusable utility functions if necessary.
- Handle potential errors within hooks gracefully.

### 2.4 Admin UI Customization:

Modifications to the Payload Admin UI should be done judiciously and only when necessary to improve content editor experience or meet specific project requirements. While customization is powerful, excessive modifications can make upgrades more challenging.

Guidance:
- Utilize Payload's built-in admin options for collections and fields before resorting to custom components.
- If custom components are required, place them in a dedicated admin/components directory.
- Document the purpose and implementation of any custom Admin UI components.




