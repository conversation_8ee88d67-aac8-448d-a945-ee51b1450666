import Viewer from 'viewerjs';
import { nextTick, onMounted, watch } from 'vue';
import type { Route } from 'vitepress';

let viewer: Viewer | null = null;

const setViewer = (el= '.vp-doc', option?: Viewer.Options) => {
    const defaultBaseOption: Viewer.Options = {
        navbar: false,
        title: false,
        toolbar: {
            zoomIn: 4,
            zoomOut: 4,
            prev: 4,
            next: 4,
            reset: 4,
            oneToOne: 4
        }
    }
    const container = document.querySelector(el);
    if (container) {
        viewer = new Viewer(container as HTMLElement, {
            ...defaultBaseOption,
            ...option
        });
    }
};

/**
 * set imageViewer
 * @param route
 * @param el The string corresponding to the CSS selector, the default is ".vp-doc img".
 * @param option viewerjs options
 */
const imageViewer = (route: Route, el?: string, option?: Viewer.Options) => {
    onMounted(() => {
        setViewer(el, option);
    })
    watch(() => route.path, () => nextTick(() => {
        viewer?.destroy();
        setViewer(el, option);
    }));
}

export default imageViewer;