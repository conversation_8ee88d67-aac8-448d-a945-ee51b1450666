<template>
    <div class="image-viewer" :style="inline ? {display: 'inline-block', margin: 0} : {}">
        <img class="hide-image-element" :src="src" alt="">
    </div>
</template>

<script setup lang="ts">
    import 'viewerjs/dist/viewer.min.css'

    withDefaults(defineProps<{
        alt: string
        src: string
        inline?: boolean
    }>(), {
        inline: false
    });

    const showImage = (e: Event) => {
        ((e.target as HTMLElement).previousElementSibling as HTMLElement).click();
    };
</script>

<style scoped lang="css">
    .image-viewer {
        margin: 20px 0;
    }
</style>