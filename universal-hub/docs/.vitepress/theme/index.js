import 'viewerjs/dist/viewer.min.css';
import { useRoute } from 'vitepress';
import imageViewer from '../theme/image_viewer/viewer.ts';
import vImageViewer from '../theme/image_viewer/vImageViewer.vue';
import DefaultTheme from 'vitepress/theme';

export default {
    ...DefaultTheme,
    enhanceApp(ctx) {
        DefaultTheme.enhanceApp(ctx);
        ctx.app.component('vImageViewer', vImageViewer);
    },
    setup() {
        const route = useRoute();
        imageViewer(route);
    }
};

