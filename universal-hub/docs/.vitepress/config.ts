import taskList from 'markdown-it-task-lists';
import { defineConfig } from 'vitepress';

export default defineConfig({
  title: "🌐 Universal Hub",
  description: "Documentation Website",
  markdown: {
    config: (md) => {
      md.use(taskList);
    },
  },
  ignoreDeadLinks: [
    (url) => {
      return url.toLowerCase().includes("localhost");
    },
  ],
  themeConfig: {
    footer: {
      message: "Project Status",
      copyright: "POC - 0.0.1",
    },
    sidebar: [
      {
        text: "Universal Engine",
        collapsed: true,
        items: [
          {
            text: "Getting Started",
            collapsed: true,
            items: [
              {
                text: "Installation & Setup",
                link: "universal-engine/getting-started/installation-setup",
              },
              {
                text: "Environment Variables",
                link: "universal-engine/getting-started/environment-variables",
              },
              {
                text: "Database & Cache Setup",
                link: "universal-engine/getting-started/database-setup",
              },
              {
                text: "Development Rules",
                link: "universal-engine/getting-started/development-rules",
              },
              {
                text: "Project Structure",
                link: "universal-engine/getting-started/project-structure",
              },
              {
                text: "Branching Strategy",
                link: "universal-engine/getting-started/branching-strategy",
              },
              {
                text: "Error Handling",
                link: "universal-engine/getting-started/error-handling",
              },
              {
                text: "Scalar Documentation",
                link: "universal-engine/getting-started/scalar",
              },
              {
                text: "Universal Hub",
                link: "universal-engine/getting-started/universal-hub",
              },
              {
                text: "For Maintainers",
                link: "universal-engine/getting-started/maintainer",
              },
              {
                text: "Debugging Common Issues",
                link: "universal-engine/getting-started/debugging-common-issues",
              },
            ],
          },
          {
            text: "Project Progress",
            collapsed: true,
            items: [
              {
                text: "UE API List (Part 1)",
                link: "universal-engine/progress/ue-api-part-1",
              },
              {
                text: "UE API List (Part 2)",
                link: "universal-engine/progress/ue-api-part-2",
              },
            ],
          },
        ],
      },
      {
        text: "Universal Ops",
        collapsed: true,
        items: [
          {
            text: "Concepts",
            link: "universal-ops/concepts",
          },
          {
            text: "Installation & Setup",
            link: "universal-ops/installation-setup",
          },
          {
            text: "Collections",
            link: "universal-ops/collections",
          },
          {
            text: "Access Controls",
            link: "universal-ops/access-controls",
          },
          {
            text: "Development Rules",
            link: "universal-ops/development-rules",
          },
          {
            text: "Project Structure",
            link: "universal-ops/project-structure",
          },
          {
            text: "Environment Variable",
            link: "universal-ops/environment-variables",
          },
          {
            text: "Debugging Common Issues",
            link: "universal-ops/debugging-common-issues",
          },
        ],
      },
    ],
  },
});
