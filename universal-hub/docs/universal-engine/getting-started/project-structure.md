---
prev:
  text: "Database & Cache Setup"
  link: "../getting-started/database-setup.md"
next:
  text: "Branching Strategy"
  link: "../getting-started/branching-strategy.md"
---

[⬅️ **Main**](../../index.md)

# 🏗️ Project Structure

Explore the new vs. existing project structure! In this section, we'll walk you through how the new project structure is set up. You can dive deeper into the architecture by clicking this [link](../architecture/architecture.md).

## 🏛️ Existing Project Structure

Here's a look at the previous project structure:

![MS Project Structure](../../img/universal-engine/getting-started/project-structure/ms_project_structure.png)

![MS Util Project Structure](../../img/universal-engine/getting-started/project-structure/ms_project_structure_util.png)

## 🚀 New Project Structure

Our shiny new modular monolithic structure:

![Monolithic Project Structure](../../img/universal-engine/getting-started/project-structure/monolithic_project_structure.png)

## 🌱 Root Directory

The `root` directory contains all the essential configurations for the project. Here are the key folders and files:

- `.github` — stores CI/CD pipeline configuration.
- `.husky` — contains check automation settings for pre-commit hooks.
- `docs` — stores the docs you're reading now, along with images and VitePress configs.
- `environments` — store .env configarations for different envirments.
- `postgres` — configuration files used to connect to the PostgreSQL database.
- `migration` — keeps SQL files and snapshots generated by Drizzle, useful for database migrations.
- `node_modules` - contains all the installed dependencies (libraries, tools, frameworks, etc.)
- `src` — holds all the source code (details coming up next!).
- `env.config.json` — contains backend URLs, with each environment’s URLs specified.
- `package.json` — lists all the project dependencies and scripts.

## 🗂️ SRC Directory

In our new architecture, we’ve transitioned from microservices to a **monolithic microservice** approach. The `src` directory is where the magic happens. Below are the key folders:

- `config` — stores DB and cache connection settings.
- `enum` — contain ts files that define enums or constant values used throughout the application.
- `middleware` — contains custom error and authentication handling.
- `shared` — keeps common schemas and helper functions used across the project.
- `modules` — houses the core API logic and functionality.
- `integration` — stores API calls to backend systems (e.g., WSO2 and OMG).

Naming convention:

- All folder and file names are lowercase.
- Two file types: `const` files uses Camel Case, while `class` files uses Pascal Case.
- Tables and columns names must use snake case.

> [!IMPORTANT] 💡 Note
> Use Camel Case for **constants/variables** and Pascal Case for **types**.

### 🛠️ Modules

Each module contains API versions and features. The key folders within each module include:

- **`controllers`** — Serves as the API entry point, detailing the API for documentation.
- **`schemas`** — Defines data types and request/response structures. This folder is split into:
  - **`/api`** — Contains API-specific schemas for requests and responses.
  - **`/models`** — Contains database models (table models) used for database interactions.
- **`services`** — Contains the API business logic.
- **`helpers`** — Houses additional logic to improve readability in service functions. If a helper is used across modules, place it in the `shared` folder instead.

Each module has an `index.ts` file, which serves as the module's entry point. Import all controller files here and handle API authorization checks.

![index.ts](../../img/universal-engine/getting-started/project-structure/index.png)

### 📊 Microservice to Module Comparison Table

Check out how the old microservices map to the new modules:

| #   | Module Name  | Microservices                                                                               |
| --- | ------------ | --------------------------------------------------------------------------------------------|
| 1   | Address      | Coverage                                                                                    |
| 2   | Batch        | Batch                                                                                       |
| 3   | Catalogue    | Catalogue, Campaign & Profiling                                                             |
| 4   | Eligibility  | Campaign, Profiling, Ticketing, Ordering & Global                                           |
| 5   | Notification | Notification & Ticketing                                                                    |
| 6   | Order        | Ordering, Ticketing, Campaign & Catalogue                                                   |
| 7   | Payment      | Payment                                                                                     |
| 8   | Record       | Ticketing, Tracking & Service                                                               |
| 9   | Reward       | Campaign                                                                                    |
| 10  | Security     | Security & Notification                                                                     |
| 11  | Setting      | Services                                                                                    |
| 12  | User         | Profiling, Billing & Catalogue                                                              |
| 13  | Util         | Util, Global, Network & Ticketing                                                           |
| 14  | Internal     | Cache tool for dev                                                                          |
| 15  | Temporal     | Customer checking , Troika integration,  email notification,order mmanagement, user task    |

### 🔗 Integration

The `integration` folder stores all external service calls. Functions are organized by external service, and for WSO2, they are further separated by module. Each integration contains a `schemas` folder and a class file. Like the modules, this folder has an `MwIntegration.ts` entry file that imports all other files.

![MwIntegration.ts](../../img/universal-engine/getting-started/project-structure/mwintergration.png)
