---
prev:
  text: "Branching Strategy"
  link: "../getting-started/branching-strategy.md"
next:
  text: "Scalar Documentation"
  link: "../getting-started/scalar.md"
---

[⬅️ **Main**](../../index.md)

# 🚦 Error Handling & Logging

## 🛡️ Error Response Implementation: Your API Safety Net

![Error Class](../../img/universal-engine/getting-started/observability/ue_error_class.png)

![Sample Error](../../img/universal-engine/getting-started/observability/unauthorized_error_invalid_key.png)

We’ve got error handling covered across the project so you can focus on building without worrying about reinventing the wheel. Here’s a quick breakdown of our trusty error response:

- **✅ Success**: This boolean flag tells you if the API request succeeded (`true` for 200 & 300 status codes, `false` otherwise).
- **📝 Name**: A short description of the error.
- **🔢 Code**: The HTTP status code of the response. This is used to show business errors.
- **📋 Cause**: A JSON object detailing the reason behind the error.
- **💬 Message**: Optional but useful for adding more context to the error.
- **🔗 IntegrationId**: A unique ID linking the error logs with the original API request (more on this later).
- **📥 Response**: An optional JSON object for storing backend system error responses.

To use the error response, just call the error class constructor, passing in the message, code, and cause details. The error will also be logged automatically!  
![Error Call](../../img/universal-engine/getting-started/observability/ue_error_call.png)

### 🧾 Error Code Cheat Sheet

Here’s a quick guide to help you decode error messages:

| Status Code | Description                 |
| ----------- | --------------------------- |
| 401         | Unauthorized 🚫             |
| 403         | Forbidden 🔒                |
| 404         | Not Found 🔍                |
| 406         | Not Acceptable              |
| 409         | Conflict                    |
| 422         | Unprocessable Entity        |
| 429         | Limit Exceeded              |
| 500         | Internal Server Error 🔧    |
| 503         | WSO2 Error Response         |
| 503         | OMG Error Response          |
| 503         | Cloud Connect Error         |
| 503         | eCommerce Error Response 🛒 |
| 520         | Unknown                     |

> [!NOTE] 💡 Pro Tip
> You can learn more about HTTP Status Code from this [link](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status).

## 👀 Observability: Keeping Everything in Check

To help you monitor API health and ease debugging, we’ve implemented logging and tracing using ElysiaJS's OpenTelemetry and Pino plugins. Here’s a sneak peek at what logs look like in action:

![Request Log](../../img/universal-engine/getting-started/observability/request_log.png)  
![Response Log](../../img/universal-engine/getting-started/observability/response_log.png)

Logs are formatted as JSON objects, with details for both requests and responses. Here’s what to look out for:

- **📡 Request**: All the essentials like URL, query parameters, headers, and the request body.
- **📦 Response Body**: Includes everything you need from the API response, including the `x-request-id`.
- **🆔 x-request-id**: A unique ID for each API request, allowing the developer to track down unexpected errors quickly.
- **🔗 IntegrationId**: This unique ID connects the dots between the API request and all related activities (like external calls and database transactions). You’ll find it in the response body.

## 👨🏻‍💻 PII

PII stands for Personally identifiable information. These are data that could identify a specific person. Examples of these data are name, government-issued ID number, date of birth, occupation, or address.

In this department, the data below are considered as PII:

- Name
- Government-issued ID type and number (IC and Passport)
- Customer and Billing Account Number
- Customer, Billing and Service Address
- Email
- Mobile Number

These data needed to be handled properly. As of now the procedures of handling these kind of data are as below:

1. ID type and ID values are retrieved through the session ID
2. Customer and Billing Account numbers are sent encrypted in all API request. The encrypted value is given to FE at the homepage API (profile/account)
3. All PII except for ID value are encrypted before storing it in tables. ID value is hashed instead.
4. Developers are **NOT ALLOWED** to set any PII data as logs. The exception of this rule are intercept logs (UE to Backend API call logs).
