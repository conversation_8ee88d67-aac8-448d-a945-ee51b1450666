# 🔧 Debugging Common Issues

## Issue 1: `init-db` Container Won’t Start

Error message:

```bash
/usr/local/bin/docker-entrypoint.sh: line 351: /docker-entrypoint-initdb.d/init-db.sh: cannot execute: required file not found
```

#### Cause:

This typically occurs on Windows, where the line endings of the `postgres/init-db.sh` file are converted from Unix (LF) to Windows (CRLF).

#### Solution:

Convert the `init-db.sh` file back to Unix format. Here's how with Notepad++:

1. Open the `init-db.sh` file in Notepad++.
2. Go to **Edit > EOL Conversion > Unix (LF)**.
3. Save the file.

Here's how to do it in VSCode:

1. Open the `init-db.sh` file in VSCode.
2. Go to rigth side of Status Bar (The bar at the bottom of the window), the End of Line Sequence option is beside the encoding option
   ![Status Bar](../../img/universal-engine/getting-started/vscode-extensions/crlf.png)
3. Once clicked, options will be shown on the top of the window. Select LF
   ![CRLF Option](../../img/universal-engine/getting-started/vscode-extensions/crlf-option.png)

Once converted, the container should run without any issues!

---  

## Issue 2: Docker Desktop Fails to Start (Windows Users Only)  

#### Cause:  

This typically occurs when you don't have the admin privileges to run Docker Desktop.

#### Solution:  

1. **Run PowerShell as Administrator:**  
   Open PowerShell with administrative privileges.  

2. **Enable Windows Features:**  
   Run the following commands to enable required features:  
   ```bash  
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart  
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart  
   dism.exe /online /enable-feature /featurename:Microsoft-Hyper-V /all /norestart  
   ```  

3. **Add Your Account to the Docker Users Group:**  
   Replace `${your_account}` with your username and run:  
   ```bash  
   net localgroup docker-users "tmmaster\${your_account}" /add  
   ```  

4. **Enable Hyper-V:**  
   ```bash  
   bcdedit /set hypervisorlaunchtype auto  
   ```  

5. **Install WSL (Windows Subsystem for Linux):**  
   ```bash  
   wsl --set-default-version 2  
   wsl --install -d Ubuntu  
   ```  
   Ensure the installation is performed in your user directory.  

6. **Restart Your Computer:**  
   Reboot your system to apply the changes.  

After completing these steps, Docker Desktop should start successfully.

---
## Issue 3: `init-db` Fails on macOS Due to Permission Denied

Error message:

```bash
permission denied: ./postgres/init-db.sh
```

#### Cause:

On macOS, the init-db.sh script may not have the proper execution permissions, causing Docker to fail when trying to run it.

#### Solution:

Grant execution permission to the script using the following command:

```bash
chmod +x ./postgres/init-db.sh
```

Then, restart your Docker container:

```bash
docker-compose restart
```

This will ensure the script is executable, allowing the container to start successfully.

## 🛠 Enterprise API Support Contacts

If you encounter any issues with the Enterprise API, please contact the respective support teams:

📌 WERAS – 📧 <EMAIL>

📌 WSO2 – 📧 <EMAIL>

📌 OMG – 📧 <EMAIL>

For a faster resolution, include relevant details such as error messages, request payloads, and timestamps in your email. 