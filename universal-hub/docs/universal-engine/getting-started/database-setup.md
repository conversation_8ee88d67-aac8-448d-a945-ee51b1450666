---
prev:
  text: 'Environment Variables'
  link: '../getting-started/environment-variables.md'
next:
  text: 'Project Structure'
  link: '../getting-started/project-structure.md'
---

[⬅️ **Main**](../../index.md)

# 🚀 Database & Cache Setup

## PostgreSQL – Your Database Powerhouse

In this project, we're harnessing the power of PostgreSQL to manage data. The good news? You won’t have to set up the database from scratch—our Docker Compose file does the heavy lifting for you.

## How to Get Local PostgreSQL Up and Running:

1. Crack open your terminal, and make sure you're in the project directory.
2. Run the Docker Compose file. Need a refresher on how to do that? No worries! Check out the [Running the Application](./installation-setup.md#-running-the-application) section for a quick guide.
3. Once Docker Compose is running smoothly, you can connect to PostgreSQL using Drizzle or DBeaver.

### Drizzle & Drizzle studio
Drizzle is a neat database toolkit package that lets you write SQL-like queries with full type safety. It also allow us to manage the data and table visually by using their studio, Drizzle Studio.

To start the studio, you just need to follow these steps:

1. Ensure the database port is up and running in Docker.
2. Start Drizzle Studio by running this command: `bun db:studio`. Open the link given in the terminal.

![Drizzle Studio Link](../../img/universal-engine/getting-started/database-setup/drizzle_studio_link.png)

This what the website should look like:

![Drizzle Studo](../../img/universal-engine/getting-started/database-setup/drizzle_studio.png)

3. Generate SQL schema by running this command: `bun db:generate`

4. Run the SQL schema by running this command: `bun db:migrate`

5. You should see the tables created in the database.

### DBeaver
Dbeaver is another database platform similar to Drizzle Studio. You can download it from [here](https://dbeaver.io/download). Here’s a peek at what connecting through DBeaver looks like:

![DBeaver](../../img/universal-engine/getting-started/database-setup/dbeaver.png)

> [!NOTE] 💡 Pro Tip
> Credentials are safely tucked away in the `environments` folder—just search for `DB_USER`, `DB_PASSWORD`, `DB_NAME`, and `DB_PORT` based on your environment.

And that's it—you’re all set with PostgreSQL!

## Connecting to the UA Remote Database
In some cases, you’ll need to connect to the UA SIT (User Acceptance Testing) PostgreSQL database to either test your data or verify application functionality. 

Here’s how you can make that connection:


### How to Connect to UA SIT Database:

### 1. Application Level

![sit_db_Auth](../../img/universal-engine/getting-started/database-setup/sit_db_server_auth_window.jpg)

You will need the following details for server Auth where you can get from the ```environments``` folder based on your preferred environment:
   - **DB_HOST**
   - **DB_PORT**
   - **DB_NAME**
   - **DB_USER**
   - **DB_PASSWORD**

You will need the following details for SSH setup:

![sit_db_SSH](../../img/universal-engine/getting-started/database-setup/sit_db_server_auth_window_jump.jpg)

   - **Host:** **************
   - **Port:** 22
   - **Username:** Your staff id
   - **Password:** Your Jump Host password

### 2. Developer Level

![sit_db_Auth](../../img/universal-engine/getting-started/database-setup/sit_db_server_auth_window_dev.jpg)

You will need the following details for server Auth where you can get from the ```environments``` folder based on your preferred environment:

   - **DB_HOST**
   - **DB_PORT**
   - **DB_NAME**
   
Except for the DB_USER and DB_PASSWORD, each developer will have their own DB_USER and DB_PASSWORD. Please contact the team for the details.
   - **DB_USER:** Your staff id
   - **DB_PASSWORD:** your password

You will need the following details for SSH setup:

![sit_db_SSH](../../img/universal-engine/getting-started/database-setup/sit_db_server_auth_window_jump.jpg)

   - **Host:** **************
   - **Port:** 22
   - **Username:** Your staff id
   - **Password:** Your Jump Host password

Now You can test your ssh first and if its establized then check the db connection.
Here you go..!

## Redis – Your Go-To Cache

Redis is our cache server of choice for this project. You have a couple of ways to interact with Redis: using Docker Desktop or the good ol’ command line.

### 🔍 Accessing Redis via Docker Desktop

If Docker Desktop is your jam, follow these steps:

1. Fire up Docker Desktop.
2. Head over to the 'Containers' tab.
3. Find the 'redis' container and give it a click.
4. Jump to the 'Exec' tab and open the terminal.
5. Now, to enter the Redis realm, run:

   ```bash
   redis-cli
   ```

6. Want to see all the keys stored in Redis? Just type:

   ```bash
   KEYS *
   ```

> 💡 **Pro Tip**: Redis has a whole arsenal of commands—check out the [official Redis docs](https://redis.io/docs/latest/commands/) to unlock its full potential.

7. Once you’re done, exit the Redis CLI with:

   ```bash
   exit
   ```

### 🖥️ Accessing Redis via Command Line

If you're more of a terminal person, here's the quick and easy way to access Redis:

1. Pop open your terminal and navigate to the project directory.
2. Fire up the Docker Compose file.
3. Once everything's running, dive into Redis with this command:

   ```bash
   docker-compose exec redis redis-cli
   ```

4. Explore Redis to your heart’s content using commands like:

   ```bash
   KEYS *
   ```

5. Wrap it up by exiting with:

   ```bash
   exit
   ```

---

That’s your quick tour of setting up PostgreSQL and Redis! Ready to jump in? Let’s keep the data flowing and the cache blazing! 🔥
