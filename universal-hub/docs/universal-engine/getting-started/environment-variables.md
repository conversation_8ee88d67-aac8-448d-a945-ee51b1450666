---
prev:
  text: "Installation & Setup"
  link: "../getting-started/installation-setup.md"
next:
  text: "Database & Cache Setup"
  link: "../getting-started/database-setup.md"
---

[⬅️ **Main**](../../index.md)

# 🌱 Environment Variables

Welcome to the secret sauce of the project—**environment variables**! These little gems keep all our sensitive info, like API keys and database passwords, safe and configurable across environments.

## 🔑 Key Environment Variables

Here's a rundown of the main environment variables you'll be working with:

- **ENVIRONMENT:** Tells the app which environment it's running in (e.g. `dev`, `sit`, `uat`, `preprod`).
- **X_API_KEY:** API key for accessing our awesome APIs.
- **SECRET_KEY:** Your app's secret weapon for encrypting and decrypting data.
- **SALT_KEY:** The salt key to ensure your encryption stays solid.
- **WERAS_SECRET_KEY:** Secret key used for encrypting payloads before sending them to WERAS.
- **WERAS_SALT_KEY:** Salt key used in conjunction with `WERAS_SECRET_KEY` for secure encryption when communicating with WERAS.
- **POSTGRES_DB:** The name of our trusty super database.
- **POSTGRES_USER:** The database's top-tier superuser.
- **POSTGRES_PASSWORD:** The password to unlock that superuser.
- **DB_HOST:** Host location of the database.
- **DB_PORT:** The port where the database listens.
- **DB_USER:** Username for accessing the database.
- **DB_PASSWORD:** Password for database access.
- **DB_NAME:** Name of the application database.
- **REDIS_HOST:** Where our Redis server resides.
- **REDIS_PORT:** The port number Redis listens on.
- **AXIOM_TOKEN:** Temporary Axiom API token (we're transitioning to Grafana soon!).
- **AXIOM_DATASET:** The dataset used by Axiom.
- **JWT_SECRET:** Secret key used to sign and verify JWT tokens.
- **SCALAR_USERNAME:** Username for accessing API documentation.
- **SCALAR_PASSWORD:** Password for API documentation access.
- **UDID_SYSTEM_SECRET:** Secret key used for encrypting and decrypting UDID (Unique Device Identifier) data.
- **WSO2_KCI_API_KEY:** API key used to generate a hash for validating the bill hash received from WSO2.
- **WHITELIST_CREDENTIALS:** A predefined list of credentials that bypass TAC (Transaction Authorization Code) validation and are allowed to log in directly.

### 📋 Running the Project in Different Environments

guide will show you how to modify the docker-compose.yml file and environment configuration files to run the project in different environments (e.g., Development, SIT, Production). By changing the necessary environment variables and Docker Compose settings, you can easily adapt the project to various environments.

## 🛠️ Steps to Change the Environment Configuration

### Prepare Environment-Specific Files

For each environment, you need a corresponding .env file that contains all the necessary environment variables. These files will be read by Docker Compose and injected into the containers.

### Example Environment Files

- **Development:** `./environments/.env.dev`
- **System Integration Testing (SIT):** `./environments/.env.sit`
- **Production:** `./environments/.env.prod`

These files should contain configuration variables that are specific to each environment, such as database credentials, API keys, or Redis configuration. Here’s an example of an .env.dev file:

![Dev Docker Compose](../../img/universal-engine/getting-started/environment-variable/env_dev.png)

Make sure each .env file is properly configured for the target environment.

Modify docker-compose.yml to Point to the Correct Environment File
In the docker-compose.yml file, the environment configuration is handled through the env_file directive.

This defines which environment file will be used by the containers.

By default, the docker-compose.yml file may be set to use ./environments/.env.dev. If you want to run the project in a different environment (like SIT or Production), you need to update this reference.

### Example Configuration for Development (.env.dev):

Switch Between Different Environments
To switch between environments, simply update the env_file paths in the docker-compose.yml file to point to the appropriate .env file.

```markdown
## Environment Configuration Files

| Environment    | File Path                               |
| -------------- | --------------------------------------- |
| Development    | `env_file: ./environments/.env.dev`     |
| SIT            | `env_file: ./environments/.env.sit`     |
| Pre-Production | `env_file: ./environments/.env.preprod` |
| UAT            | `env_file: ./environments/.env.uat`     |
```

You will need to manually change the env_file in the docker-compose.yml file depending on which environment you want to run.

### Rerun the docker-compose command.

And that’s it! You’re now armed with all the environment variable goodness to get your project running smoothly in any environment. 🔐

---

By keeping your environment variables organized and secure, your development process stays streamlined and your secrets stay safe! 🚀
