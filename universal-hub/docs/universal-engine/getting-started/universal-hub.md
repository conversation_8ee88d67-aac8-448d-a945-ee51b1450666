---
prev:
  text: 'Scalar Documentation'
  link: '../getting-started/scalar.md'
next:
  text: 'For Maintainers'
  link: '../getting-started/maintainer.md'
---

[⬅️ **Main**](../../index.md)

# 📚 Universal Hub

Welcome to the **one-stop center** for developers to get onboard with the project! This documentation website is created using the awesome [VitePress](https://vitepress.dev/) plugin. You can access the website through these environment links:

| 🌐 **Environment** | **URL**                                             |
| ------------------ | --------------------------------------------------- |
| Prod               | https://didactic-adventure-re12ey6.pages.github.io/ |

> [!WARNING]⚠️ Caution
> Keep in mind that this website has **local and production environments only** when making changes or creating a new documentation. The production environment is based off the main branch.

## 🗂️ Folder Structure

![Docs Structure](../../img/universal-engine/getting-started/project-structure/docs_structure.png)

The documentation is organized into four main components:

- **`.vitepress`**: Stores all VitePress packages and website configurations.
- **`img`**: Contains all images used across the website, organized by application, major topics and documentation sections.
- **`universal-engine`**: Holds all markdown documentation pages related Universal Engine, organized by major topics, with an `others` folder for supporting sub-pages and scripts.
- **`index.md`**: The entry point of the website.

## ✍️ Creating a Documentation Page

Before starting, consider the following:

- **How many pages** are needed for the documentation?

> [!CAUTION]⚠️ Caution
> VitePress has a rendering limit that’s not necessarily tied to the number of code lines, so avoid making excessively long documents.

- Ensure the **folder structure** for images and pages is well-organized.
- Set up **navigation** with proper entry and exit points from existing pages and the sidebar.
- Use **clear and meaningful** file and image names. Use **Kebab Case** for file names and **Snake Case** for image names
- Plan for the **update frequency** of the documentation.

All pages are in `.md` format, as VitePress supports Markdown. If additional markdown annotations (like checkboxes) are needed, request approval from the maintainers for extra packages, and update the `config.ts` file accordingly.

### 🧭 Navigation Setup

To display a new page in the documentation:

- For **previous** and **next** links, include this code at the beginning of each page:

![Prev Next Links](../../img/universal-engine/getting-started/vitepress/prev-next-link.png)

- To add the page to the **sidebar**, update the `config.ts` file like so:

![Sidebar](../../img/universal-engine/getting-started/vitepress/sidebar-code.png)

> 💡 **Pro Tip:** Ensure consistency with the page names in the content, sidebar, and previous/next links.

## 🚀 Running the Documentation Website Locally with Bun

1. In your terminal, run the following command:

```bash
bun run dev
```

Once successful, you'll see:

![Vitepress](../../img/universal-engine/getting-started/vitepress/vitepress.png)

2. Open `localhost:5173` in your browser, and you’re good to go! 🎉
