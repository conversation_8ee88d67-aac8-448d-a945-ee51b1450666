---
prev:
  text: "Error Handling"
  link: "../getting-started/error-handling.md"
next:
  text: "Universal Hub"
  link: "../getting-started/universal-hub.md"
---

[⬅️ **Main**](../../index.md)

# 📜 Scalar Documentation (API Specification)

> [!CAUTION]⚠️ Caution
> Website below is for **INTERNAL USE** only. Do not share the links and access to external parties without verbal and written permission

![Scalar Home Page](../../img/universal-engine/getting-started/scalar/home_page.png)

Our API documentation, commonly called **Scalar Documentation**, is created using the [Scalar](https://github.com/scalar/scalar) plugin. Here's what it does:

- Lists all the APIs available in the project 📋
- Describes the backend systems and DB tables involved 🛠️
- Provides request requirements and schema 📥
- Displays the response schema 📤
- Allows API testing 🧪

You can access the documentation through the following URLs:

| Environment | URL                                                                                            |
| ----------- | ---------------------------------------------------------------------------------------------- |
| Local       | localhost:3000/login                                                                           |
| SIT         | [URL]                                                                                          |
| Preprod     | [https://myunifi-dev.myu.unifi.com.my/ue/login](https://myunifi-dev.myu.unifi.com.my/ue/login) |
| Prod        | [URL]                                                                                          |

Upon accessing, you'll see a login page. Use the credentials below to log in:

![Scalar Login](../../img/universal-engine/getting-started/scalar/login.png)

| Username      | Password      |
| ------------- | ------------- |
| DeveloperDocs | DeveloperDocs |

## 🛠️ API Testing

### Common Headers

![Headers](../../img/universal-engine/getting-started/scalar/headers.png)

Each API in UE requires specific headers to function. Here are the common headers you’ll need:

- **API Key** 🔑: Varies by environment (see the table below)
- **Source** 🌐: Choose from provided options
- **Authorization** 🔒: Use a JWT token in the format `Bearer <Token>`

For each environment, here's the API key you should use:

| Environment | API Key           |
| ----------- | ----------------- |
| Local       | XXX               |
| SIT         | BV4C2PjmWbRKSyDrX |
| Preprod     | WdcZPAq8D9B7XmVFQ |
| Prod        | yQ9Ap7BHtS6WNUsr8 |

Please ensure the Authorization section as is since we already declare it in the Headers section
![Auth options](../../img/universal-engine/getting-started/scalar/auth-option.png)

### 📬 Request Types

We typically use three request types in this documentation:

- **Query**: Parameters are passed as key-value pairs in the URL.
  ![Query](../../img/universal-engine/getting-started/scalar/test_request_query.png)
- **Param**: Parameters are directly embedded in the URL.
  ![Param](../../img/universal-engine/getting-started/scalar/test_request_param.png)
- **Body**: Parameters are passed in the request body, usually in JSON format.
  ![Body](../../img/universal-engine/getting-started/scalar/test_request_body.png)

> [!WARNING]⚠️ Warning
> The `x-www-form-urlencoded` format cannot be tested directly through Scalar. While the API will be displayed, you’ll receive a "type mismatch" error if you try to test it.

## 🖥️ Displaying an API in Scalar Documentation

![Query Code](../../img/universal-engine/getting-started/scalar/code_request_query.png)

To make sure your API appears correctly in the Scalar documentation, ensure these details are set in the controller:

- **Description**: Provide a clear description of the API, the feature it’s linked to, and the backend system or database it interacts with.
- **Tags**: Specify which module the API belongs to.
- **Body/Query/Param**: Define the request schema.
- **Responses**: Describe the response schema, and optionally define schemas for different response codes.

> [!IMPORTANT]💡 Note  
> ElysiaJS defaults to handling the request body as JSON. To change this, refer to the [ElysiaJS life cycle guide](https://elysiajs.com/essential/life-cycle.html#parse) for instructions on how to parse other formats.
