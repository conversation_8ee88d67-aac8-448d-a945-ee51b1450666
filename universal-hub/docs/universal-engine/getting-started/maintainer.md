---
prev:
  text: 'Universal Hub'
  link: '../getting-started/universal-hub.md'
next:
  text: 'UE API List (Part 1)'
  link: '../progress/ue-api-part-1.md'
---

[⬅️ **Main**](../../index.md)

# 🛠️ For Maintainers

> [!WARNING]⚠️ Heads Up!  
> This page explains the key roles of maintainers. Before diving in, make sure you’ve read and understood the [Branching Strategy](../getting-started/branching-strategy.md), [Project Structure](../getting-started/project-structure.md), and [Universal Hub](../getting-started/universal-hub.md) pages.

## 🤝 Developers vs Maintainers

- **Developers** are the builders—responsible for developing features and fixing bugs while following the repo’s rules.
- **Maintainers** are the gatekeepers—enforcing those rules and ensuring the repo stays healthy.

Some key responsibilities of maintainers include:

- 📋 Managing and reviewing pull requests (PR) according to version control standards
- 🌱 Managing branches in the repo
- 🧐 Checking and providing feedback on code quality and adherence to conventions
- 🌳 Creating necessary branches and tags for releases or reverts
- 🔄 Regularly checking, upgrading, and testing project dependencies
- 📝 Updating documentation pages and the README when necessary

## 🌿 Maintaining Branches

When maintaining branches, here are a few things to keep in mind:

1. **EVERYONE** can create and merge to non-production branches unless told otherwise, but only **MAINTAINERS** can delete them.
2. Follow naming conventions when creating protected branches.
3. In the release branch, set the version following [semver](https://semver.org/) in the `package.json`. This can be done before or after merging the PR.
4. Ensure all branches are ahead of `main`.
5. Periodically delete branches that don’t meet standards, such as:
   - 🕰️ Stale branches
   - 🌱 Environment branches that are behind `main`
   - 🌍 Multiple environment branches that are not the latest

## 🚦 Approving PRs

Approving a PR can feel daunting, but here’s a checklist to help you decide:

1. ✅ Ensure the PR has a successful pipeline and all related discussions are resolved.
2. 🔄 Check if the development branch is ahead of `main`.
3. 🛠️ Review the code to confirm no configurations or setups have been changed unnecessarily.
4. 🚨 Look for any breaking changes in the commits.
5. 🔍 Make sure there are no commented-out code blocks lingering.
6. 🏷️ Verify that files and folders are named properly.
7. 🔧 Run the application locally from the development branch, and test a few APIs to catch any errors before approving.
8. 🔗 Don't just focus on the changes—review how they fit within the entire codebase and project structure. If something’s unclear, ask the developer for clarification.

## 🏷️ Writing Tags

Tags/releases keep track of features and fixes sent to production. Here's how to do it right:

1. All tags should be created _after_ the release branch has been merged into `main`. The tag's target must be the **main branch**.
   ![Create Tag](../../img/universal-engine/getting-started/git/create_tag.png)
2. Name the tag according to the version in the release branch.
3. In the tag's description, include a brief summary of what was changed.

## 🔗 Dependencies

Dependencies are crucial, and we want to avoid chaos from constantly shifting versions. To keep things stable, only maintainers are allowed to upgrade dependencies.

### Checking Dependencies 🕵️‍♂️

To check the current dependencies, run:

```bash
bun depcheck
```

### 🚀 Upgrading Dependencies

When a dependency needs to be upgraded, follow these steps:

1. 📄 Review the dependency's CHANGELOG to understand what’s changed.
2. ⚠️ Check for known issues with the new version. **If there are breaking issues, DO NOT proceed!**
3. To upgrade the dependency, run:

```bash
bun update [dependency_name]@[version]
```

> ℹ️ This will also update your `package.json`.

4. Start the application locally and test it, including some API calls, to ensure there are no errors.
5. If successful, test on environment branches before merging to `main`.
6. Once merged, monitor for any issues related to the upgrade.

> 💡 Pro tip: Notify the rest of the team so they can be on the lookout for any related issues!

<!--DO NOT DELETE: The comment below is to include the maintainers' contact details-->
<!--@include: ../others/maintainer-team.md -->
