---
prev:
  text: ''
  link: '../getting-started'
next:
  text: ''
  link: '../getting-started'
---

[⬅️ **Main**](../../index.md)

# 📜 Development Rules

To ensure consistency, efficiency, and maintainability in our development process, the following rules and guidelines must be followed by all team members.

*For codes:*
- Use **Camel Case** for **constants/variables** and **Pascal Case** for **types**.
- Avoid using `any` or `unknown` data types unless absolutely necessary and properly justified. Always strive for precise typing to maintain type safety and code clarity. Usage of these 2 data types will be questioned by the maintainers during PR.
- Developers should avoid adding generic or redundant comments such as `Return the response body`. Comments should provide meaningful context, explaining why a particular implementation is necessary rather than describing what the code does, which should already be clear from the code itself.
- All PIIs need to be encrypted before leaving the API (ie: returning a response & storing into DB)

*For tables:*
- Tables and columns names must use **Snake Case**.
- All tables must have **id, created_at** and **updated_at** columns. This will ease the development on Universal Ops if the table is needed on that side.