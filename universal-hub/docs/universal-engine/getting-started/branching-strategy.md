---
prev:
  text: 'Project Structure'
  link: '../getting-started/project-structure.md'
next:
  text: 'Error Handling'
  link: '../getting-started/error-handling.md'
---

[⬅️ **Main**](../../index.md)

# 🌿 Branching Strategy

## Main Branch

The **main branch** is the production branch. Developers are not allowed to create a pull request to the main branch. All pull requests must be made to a release branch to be merged into the main branch.

## Development Branch

To start development, a developer must create one of three development branches based on their task: **feature**, **bugfix**, or **hotfix**. Here are the rules to follow when creating and maintaining a development branch:

1. All branches must be created from the main branch.

   ![Create Branch](../../img/universal-engine/getting-started/git/create_branch.png)

2. Rebase the short-lived development branch whenever there are new changes in the main branch.
3. Ensure the branch is rebased based on the main branch.
4. Do not allow merge commits in your branch.
5. **DO NOT EVER PUSH DIRECTLY INTO PROTECTED BRANCHES; EVERYTHING MUST GO THROUGH A PULL REQUEST.**

### Development Branch Rules

- Always check that you are in the correct branch when making changes.
- Ensure your branch is up-to-date before pushing to the remote repository by using `git pull` and `git rebase` to resolve conflicts.
- Leave meaningful comments, especially for business logic-related code.
- Use TODO and FIXME comments to note future tasks for yourself or other developers.

### Writing Commit Messages

Every commit message must start with one of these keywords:

- **feat:** Indicates new features or enhancements.
- **fix:** Indicates changes made to fix parts of the code.
- **chore:** Indicates clean-up tasks after rebase or fixes related to linting or dependency checks.
- **docs:** Indicates changes made for the documentation.

If changes introduce breaking changes, append an exclamation mark (!) to the type. **Example**: `feat!: test data`.

> [!NOTE]💡 Pro Tip
> For more details on writing commit messages, refer to this [page](https://www.conventionalcommits.org/en/v1.0.0/).

## Non-Production Branch

> [!IMPORTANT]⚠️ IMPORTANT
> There can be **ONLY** one branch per environment at a time. If there is more than one branch, notify the maintainers for deletion.

After development, a developer can merge their branch into the **SIT** and subsequently the **Preprod** branch using a Pull Request (PR). Non-production branches must be refreshed (i.e., delete the old branch and create a new one) after every deployment. These branches can be created under two conditions:

1. The branch does not currently exist.
   ![Branch not exist](../../img/universal-engine/getting-started/git/branch_not_exist.png)
2. The branch's commits are behind the main branch's commits. Notify a maintainer to delete this type of branch.
   ![Branch behind](../../img/universal-engine/getting-started/git/branch_behind.png)

> [!CAUTION]⚠️ Caution
> If you encounter a conflict error, resolve it **locally** before merging. **DO NOT CREATE ANOTHER BRANCH TO AVOID THE ERROR.**

## Release Branch

After testing is completed and the code is ready for production, a developer must request a **release branch** from a maintainer. After the release branch is created, the developer must use a PR to merge the development branch into the release branch.

![Start PR](../../img/universal-engine/getting-started/git/start_pr.png)  
![Create PR](../../img/universal-engine/getting-started/git/create_pr.png)

In the PR, the developer must request a maintainer to review and approve the commits and changes.

![Request Review](../../img/universal-engine/getting-started/git/request_review.png)

> [!WARNING]⚠️ Warning
> If the changes do not meet the standard, the maintainer has the right to reject the PR.

Once the PR is approved, the developer must create another PR to merge the release branch into the main branch. However, only a maintainer can merge the two branches, so please notify the maintainer to complete the PR.

![Approve PR](../../img/universal-engine/getting-started/git/approve_pr.png)

### Release Versioning

This project uses **semantic versioning** or [semver](https://semver.org/) as the guide for versioning. This is important when requesting a release branch, as developers need to set the proper version based on the type of branch that will be merged into the main branch. There are three types of increments that a version can have: \{**Rework**.**Feature**.**Fix**\}

- **Rework**: Major redo of the whole project.
- **Feature**: New features that include new APIs or significant enhancements of current APIs.
- **Fix**: Minor enhancements or fixes needed for the current APIs.

When multiple types of branches need to merge into the same release branch, **Rework** takes precedence, followed by **Feature** and **Fix**.

### Creating a Pull Request (PR)

When creating a PR, adhere to these rules:

1. **PR Title:** Use your **DEVELOPMENT BRANCH NAME** as the title of the PR.
2. **Hotfix/Bugfix:** If your branch is for a hotfix or bugfix, include the relevant GitHub issue link in the PR description.
3. **PR Description:** Although a bot will generate a default description, you are responsible for providing details on how your changes impact the application. Ensure your description covers:
   - The purpose of the PR.
   - A summary of the changes made.
   - How the PR affects the application.
   - Any potential breaking changes.
   - Instructions for testing.
   - Testing results.
   - Any additional relevant information.
     ![PR Request](../../img/universal-engine/getting-started/git/request_pr.png)
4. **Maintainer Assignment:** You **MUST** assign a maintainer to the PR and notify them once the PR is created.
5. **Branch Deletion:** After merging into the main branch, you **MUST** delete your development branch. If any issues arise post-deployment, create a new development branch for fixes.

## Revert Branch

The revert branch is a special branch created when recent commits in the main branch need to be reverted. Only maintainers can create this branch.

To create this branch:

1. Create a brand new release branch
2. Locate and open the PR which needs to revert
3. Click on the revert button. GitHub will automatically create a new revert branch and redirect to create a new PR for merging
   ![Revert PR](../../img/universal-engine/getting-started/git/revert_pr.png)
4. Set the target merge to a new release branch and the rest followed the same process when a release branch is merged into main branch
5. Upgrade the version in `package.json` by 0.0.1

## Branch Naming

The table below shows the naming format for each type of branch:

| #   | Branch  | Usage Example                |
| --- | ------- | ---------------------------- |
| 1   | main    |                              |
| 2   | release | release/v1.38.0              |
| 3   | revert  | revert-17-release/v1.38.0    |
| 4   | sit     | sit/2024-03-31               |
| 5   | uat     | uat/2024-03-31               |
| 6   | preprod | preprod/2024-03-31           |
| 7   | staging | staging/2024-03-31           |
| 8   | feature | feature/task-1               |
| 9   | bugfix  | bugfix/issue-1               |
| 10  | hotfix  | hotfix/issue-2               |

## Environment Purpose

The environment branch is used to deploy the application to the corresponding environment. The branch name is used to determine the environment. For example, the `sit` branch is used to deploy the application to the SIT environment, and the `preprod` branch is used to deploy the application to the Preprod environment. You may find the following table useful in understanding the purpose of each environment branch:

| Branch       | Environment    | Key Stakeholders              | Primary Purpose                                                  | Testing Type                                | Data Type                    | Enterprise System |
| ------------ | -------------- | ----------------------------- | ---------------------------------------------------------------- | ------------------------------------------- | ---------------------------- | ----------------- |
| main         | Production     | End-users and Business        | Serve real customers                                             | Monitoring / Smoke                          | Live / Real Data             | Production        |
| staging/\*\* | Staging        | Business, PO, QA (Digital)    | Simulate production for final testing in controlled environments | Performance Test / Pen Test / Regression    | Live / Real Data             | Production        |
| preprod/\*\* | Pre-production | PO, QA (Digital), DevOps      | Final validation before production                               | Deployment Testing / Restore Testing        | Production-like data         | Pre-Prod          |
| uat/\*\*     | UAT            | Business, PO, QA (IT/Digital) | Validate application meets requirement                           | User Acceptance Test                        | Test data for end-user flows | SIT/DC            |
| sit/\*\*     | SIT            | Developers, QA (Digital)      | Test system integration and interactions                         | Unit Testing and System Integration Testing | Mock or test data            | SIT/DC            |
