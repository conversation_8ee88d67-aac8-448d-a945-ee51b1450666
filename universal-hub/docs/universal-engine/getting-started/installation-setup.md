---
next:
  text: 'Environment Variables'
  link: '../getting-started/environment-variables.md'
---

[⬅️ **Back to Main**](../../index.md)

# ⚙️ Let’s Get Set Up!

Hey there, trailblazer! 🚀 Ready to fire up the Universal Engine and build something awesome? Let’s gear up with the tools and tech you’ll need.

---

## 🔧 The Cool Tech Stack Behind the Magic

We’re not just throwing stuff together here — this is a carefully crafted blend of awesome:

- 🟦 **Language:** [TypeScript](https://www.typescriptlang.org/)
- ⚡ **Framework:** [ElysiaJS](https://elysiajs.com/)
- 📐 **Type System:** [TypeBox](https://github.com/sinclairzx81/typebox)
- 🗃️ **ORM:** [Drizzle ORM](https://orm.drizzle.team/)
- 🛢️ **Database:** [PostgreSQL](https://www.postgresql.org/)
- 🚀 **Cache Layer:** [Redis](https://github.com/redis/ioredis) with `ioredis`
- 🌀 **Runtime:** [Bun](https://bun.sh/)
- 🐳 **Containerized with:** [Docker](https://www.docker.com/)
- 📘 **Docs built on:** [VitePress](https://vitepress.dev/)

---

## 🛠️ Installation Toolkit

Let’s make sure you’ve got everything you need to jump in:

### 🧰 Tools to Grab First

1. [VS Code](https://code.visualstudio.com/Download) — Your code sidekick.
2. [Bun](https://bun.sh/) — The fast boy running the show.
3. [Docker Engine](https://docs.docker.com/engine/install/) — So your app plays nice in containers.
4. [Git](https://git-scm.com/downloads) — For cloning the repo like a pro.
5. A Git client you love (like [Fork](https://git-fork.com/)) ❤️
6. [Postman](https://www.postman.com/downloads/) — So you can talk to your APIs.

> 💡 **Pro Tip:**  
> GUI or CLI? Browser Postman or IDE extension? Fancy VS Code theme? Totally your call — make it yours! 🎨

---

## 💻 VS Code Magic

Let’s make VS Code work smarter for you.

### ✨ Recommended Extensions in 1 Click

Make your dev life easier by installing the recommended workspace extensions:

1. Open your workspace folder.
2. Go to the Extensions tab (left sidebar).
3. Search for `@recommended`.
4. Click the ☁️ icon under **Workspace Recommendations** to install all at once!

![Recommended Extensions](../../img/universal-engine/getting-started/vscode-extensions/vscode_recommended_extensions.png)

> 💡 **Pro Tip:**  
> Got a better tool in mind? Swap it in and let us know! We're always evolving. 🔁

---

### 🧼 Auto Format Like a Pro

Keep your code clean and tidy with auto formatting on save:

1. Open **Settings** in VS Code.
2. Search for `editor.formatOnSave`.
3. ✅ Enable it!

![Format on Save](../../img/universal-engine/getting-started/vscode-extensions/vscode_format_onsave.png)

---

## 🚀 Fire Up the App

We’ve got two flavors for running the app — local with Docker or remote via terminal. Choose your path, dev warrior! ⚔️

---

### 🐳 Option 1: Docker Compose + Local DB

Run it all on your machine. Easy setup, clean teardown!

#### ✅ Step 1: Install Dependencies

```bash
bun install
```

#### 🔧 Step 2: Build and Run in Dev Mode

```bash
docker-compose up -d
```

#### 🔄 Step 3: Restart the App

```bash
docker-compose restart
```

#### 🎯 Restart Specific Service

```bash
docker-compose restart elysia-app
```

#### 🔍 Step 4: Check Docker Config

```bash
docker-compose config
```

#### 📜 Step 5: View Logs

```bash
docker-compose logs -f
```

You'll see something like this:

![Elysia Logs](../../img/universal-engine/getting-started/vscode-extensions/elysia_app_v1.png)

> 💡 **Bonus Tip:**  
> Docker Desktop also lets you view logs under the **Containers** tab — super handy!

#### 🛑 Step 6: Stop Everything

```bash
docker-compose down
```

---

### 🧑‍💻 Option 2: Terminal + Remote DB

When you need to connect to your team’s central database.

#### ✅ Step 1: Install Dependencies

```bash
bun install
```

#### 🧠 Step 2: Start Redis via Docker

```bash
docker pull redis
docker run -d -p 6379:6379 redis
```

#### 🔗 Step 3: Connect to Remote DB with DBeaver

1. Open **DBeaver**, click **New Connection**, select **PostgreSQL**.
2. Fill in your details from the `environments` folder:
   - DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD

![DBeaver Auth](../../img/universal-engine/getting-started/database-setup/sit_db_server_auth_window.jpg)

3. Go to the **SSH** tab and enter:
   - **Host:** `**************`
   - **Port:** `22`
   - **Username:** Your staff ID
   - **Password:** Your jump host password  
   - **Local host:** `localhost`
   - **Local port:** `6379`

![DBeaver SSH](../../img/universal-engine/getting-started/database-setup/db_connection_ssh.png)

4. Hit **Test Connection** to make sure everything’s golden ✨

---

#### ⚙️ Step 4: Update Your `.env.dev` File

You’ll need to switch from local DB to remote DB config:

- Comment out the Docker DB env vars.
- Uncomment the remote DB env vars.

Example:

![Env File](../../img/universal-engine/getting-started/environment-variable/remote_environment_variables.png)

---

#### 🚀 Step 5: Run the App!

```bash
bun dev
```

> 💡 **Environment Switch Tip:**  
> Want to run another environment like SIT, UAT, or Preprod?  
> Just change `bun dev` to `bun sit`, `bun uat`, or `bun preprod`!

---

## 🤝 Need Help?

Something acting weird? Ran into a bug?  
Drop us a GitHub issue or ping the maintainers — we’ve got your back.

Until then, happy coding! 💻💙