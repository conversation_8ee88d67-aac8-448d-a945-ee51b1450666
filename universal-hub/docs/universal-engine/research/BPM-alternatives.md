[⬅️ **Main**](../../index.md)

# BPM Alternatives

## General Overview

| Criteria                | Camunda                                                                                                    | Zeebe                                                                                       | Appian                                                                                             | Temporal                                                                                 | jBPM                                                                             | Flowable                                                                                          | Airflow                                                                                      | Activiti                                                       | Drools                                                             | Pega                                                           | Stately                                                                       | Step Functions                                                       |
| ----------------------- | ---------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------- | -------------------------------------------------------------- | ------------------------------------------------------------------ | -------------------------------------------------------------- | ----------------------------------------------------------------------------- | -------------------------------------------------------------------- |
| Focus and Use Cases     | Business process management, workflow automation, decision automation                                      | Workflow automation for microservices, cloud-native applications                            | Low-code application development, BPM, case management                                             | Workflow automation, task coordination, distributed systems                              | Business process management, workflow automation                                 | Business process management, workflow automation, case management                                 | Workflow orchestration, task scheduling, data pipeline automation                            | Business process management, workflow automation               | Business rules management, decision automation                     | Business process management, workflow automation               | State machine modeling, workflow automation                                   | Workflow orchestration, serverless computing                         |
| Architecture            | Java-based BPM engine, typically runs in an application server                                             | Cloud-native, horizontally scalable, Raft consensus algorithm                               | Web-based low-code platform, on-premises or cloud deployment                                       | Microservices architecture, distributed execution environment                            | Java-based BPM engine, typically runs in an application server                   | Java-based BPM engine, cloud-native support, microservices architecture                           | Python-based, distributed task orchestration framework                                       | Java-based BPM engine, typically runs in an application server | Java-based rule engine, typically integrated with BPM systems      | Java-based BPM engine, typically runs in an application server | Proprietary platform with rule engine and BPM capabilities                    | JavaScript-based state machine library                               |
| Features                | BPMN process modeling, decision tables, workflow execution, user task management, monitoring, integrations | BPMN support, task distribution, retries, long-running processes, cloud-native integrations | Process modeling, task management, collaboration, document management, analytics, integrations     | Workflow management, task scheduling, retries, distributed transactions, fault tolerance | BPMN support, process modeling, task management, monitoring, decision automation | BPMN support, process modeling, task management, monitoring, decision automation, case management | Workflow scheduling, task orchestration, data processing, monitoring, extensible with Python | BPMN support, process modeling, task management, monitoring    | Business rules authoring, decision tables, rule engine integration | BPMN support, process modeling, task management, monitoring    | Business rules authoring, decision tables, rule engine integration            | State machine modeling, state transitions, event-driven architecture |
| Community and Ecosystem | Large and active community, extensive documentation, forums, community events                              | Growing community, documentation, forums, community support channels                        | Strong user community, network of partners, documentation, forums, training, professional services | Growing community, documentation, tutorials, forums, GitHub discussions                  | Active community, documentation, forums, community events                        | Active community, documentation, forums, community events                                         | Active community, documentation, forums, community events                                    | Active community, documentation, forums, community events      | Active community, documentation, forums                            | Active community, documentation, forums, community events      | Strong user community, documentation, forums, training, professional services | Growing community, documentation, GitHub repository                  |
| License and Pricing     | Open-source community edition, commercial enterprise edition with additional features and support          | Open-source Apache 2.0 license, commercial support available                                | Commercial platform with licensing based on users, modules, and deployment options                 | Open-source MIT license, commercial support may be available from vendors                | Open-source community edition, commercial support available                      | Open-source community edition, commercial support available                                       | Open-source, Apache 2.0 license                                                              | Open-source community edition, commercial support available    | Open-source, Apache 2.0 license                                    | Open-source community edition, commercial support available    | Proprietary, commercial license                                               | Open-source MIT license                                              |

## Camunda

- Camunda incorporates several components that are closed source, with only the workflow engine itself, "Zeebe," being source available. It offers a Java SDK for development.
- With a longer history in the industry, Camunda boasts a larger and more mature community and ecosystem.
- Commercial versions are available, complemented by professional support and consulting services.
- Known for its emphasis on aligning business and IT, Camunda is ideal for organizations with real business processes defined by subject matter experts. Its notation is easily digestible for non-technical individuals, facilitating requirements engineering.
- While primarily deployed in centralized settings, Camunda can scale horizontally by deploying multiple instances, making it suitable for high-load scenarios.
- Typically utilized as a remote workflow engine, Camunda's "Zeebe" engine is decentralized and horizontally scalable, with the option for geo-redundant deployment.
- Camunda offers out-of-the-box connectors, including ones for Kafka or REST.
- Additionally, Camunda provides a decision engine capable of executing the DMN standard.
- Monitoring within Camunda relies on ElasticSearch, aligning with its architectural framework.

## Zeebe

- While not open-source, the source code is available.
- Utilizes the relatively new RocksDB for directly storing workflow state on disk.
- Lacks well-established, referenceable users in production.
- Employs a single queue that seamlessly supports all workflow executions.
- Utilizes BPMN 2.0, where code execution is not direct and is notably slower than native code.
- Official support for indefinitely running workflows is not provided.
- Offers support for visual workflow creation, although not as extensive as Camunda's capabilities.
- Does not include a built-in versioning process for running code.
- Lacks built-in archival functionality, though it can be implemented separately.
- Does not provide a built-in visibility tool; records must be shipped for analysis.
- Offers some level of support for signaling workflows.
- Does not offer a multi-region replication solution.
- Queries are only available through plugins.
- All historical and visibility data can be accessed optionally through ElasticSearch, though scalability may be limited by ElasticSearch's capabilities, serving as a potential bottleneck.

## Stately / XState

- Looks a lot like state chart
- Have editor that can be opened in browser
- Have typescript implementation
- Have source code ready to be implement directly after done with the diagram
- Unable to see the whole overview if the process is seperated into subprocess
- The code is in one file, can lead to messy and unmaintainable code easily
- Error handling is trickier since it used actor model. Error handling is very important for our BPM

## Appian

- Documentation is extensive and include videos
- Has certification
- Extensive UI on the modeler
- Can configure process reports
- Features that are similar to Camunda. Smaller learning curve on current standards

## Temporal

- Licensed under MIT.
- A relatively recent platform with a growing ecosystem.
- Empowers developers to craft highly reliable and scalable business logic through pure code.
- Utilizes its unique programming model for defining workflows using code, offering a Software Development Kit (SDK) with support for multiple programming languages.
- Engineered to manage large-scale workflows requiring high scalability and fault-tolerance, achieved via its decentralized architecture, facilitating horizontal scaling by adding more workers.
- Emphasizes an event-driven programming paradigm, enabling workflows triggered by external events, with native features like event listeners and timers facilitating the creation of reactive workflows.
- Supported by a pluggable database, currently offering compatibility with PostgreSQL, MySQL, and Cassandra.
- Boasts a roster of prominent users who have been running production instances for a considerable duration.
- Incorporates a built-in task queue mechanism, eliminating the need for external queues.
- Executes code directly, allowing developers to employ familiar testing and debugging processes.
- Supports workflows that can run indefinitely.
- Lacks built-in visual workflow tooling.
- Features an advanced versioning mechanism facilitating versioning of running code.
- Includes built-in archival capabilities, which can also be externally configured.
- Offers an integrated visibility API for monitoring workflows.
- Allows for sending signals to running workflows.
- Supports multi-region replication for enhanced reliability.
- Provides built-in support for queries.
