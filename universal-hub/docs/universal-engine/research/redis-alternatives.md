[⬅️ **Main**](../../index.md)

# Background

Beginning with version 7.4. Redis will be dual-licensed under the Redis Source Available License (RSALv2) and Server Side Public License (SSPLv1). Older versions will remain open source.

https://www.theregister.com/2024/03/22/redis_changes_license/
https://techcrunch.com/2024/03/31/why-aws-google-and-oracle-are-backing-the-valkey-redis-fork/amp/
https://thenewstack.io/linux-foundation-forks-the-open-source-redis-as-valkey/
https://github.com/jaredwray/cache-manager#store-engines
https://docs.nestjs.com/techniques/caching

**Nest JS Supported Cache Stores**

- Redis
- Mongodb
- FS Binary
- FS Hash
- Hazelcast
- Memcached
- Memory Store
- Couchbase
- SQLite

## Valkey

- no store cache manager available in nest js, thus need to setup differently: https://www.npmjs.com/package/node-valkey
- no docker image yet thus not sure how to test
- Official Documentation: https://github.com/valkey-io/valkey

https://docs.nestjs.com/techniques/caching#different-stores

## Redict

- no store cache manager available in nest js
- no package manager available
- Official Documentation: https://redict.io/

https://andrewkelley.me/post/redis-renamed-to-redict.html

## Memcached

- Store cache manager available in nest js: https://github.com/theogravity/node-cache-manager-memcached-store
- Package manager: https://www.npmjs.com/package/memcached

## KV Cloudflare Workers

- [Store as key value in cloudflare workers](https://ts.cloudflare.community/workers/wrangler/workers-kv/)
