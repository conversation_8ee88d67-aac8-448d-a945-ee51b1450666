---
prev:
  text: "Project Structure"
  link: "../getting-started/project-structure.md"
---

[⬅️ **Main**](../../index.md)

# Architecture

## Existing Architecture

![Old Architecture](../../img/universal-engine/architecture/main/ms_architecture.png)

XE used **Microservices** architecture where they broke the codes into loosely coupled services which can be developed, deployed, and maintained independently. Each of these services is responsible for discrete task and can communicate with other services through simple APIs to solve a larger complex business problem. There are a few problems arised due to this architecture:

- High amount of repeating codes across microservices
- Complex logging and tracing leading to common issues of logs not found
- Difficult to manage and standardize dependencies across microservices
- High maintenance cost

## Proposed Architecture

We proposed using **Modular Monolithic** architecture where we break the code into independent modules. Advantages of this architecture are as below:

- Centralized logs
- Better isolation
- Each module can follow their own logical separations
- Each module encapsulates their own features
- High reusability
- Less dependencies

Beside that, the database that we propose to use is **Postgres** because it allows us to store nested data in structured format which is more organized and easily searchable.

The proposed diagram can be seen in the image below:

![New Architecture](../../img/universal-engine/architecture/main/monolithic_architecture.png)
