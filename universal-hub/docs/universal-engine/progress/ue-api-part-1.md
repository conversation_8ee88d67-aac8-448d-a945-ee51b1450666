---
prev:
  text: 'For Maintainers'
  link: '../getting-started/maintainer.md'
next:
  text: 'UE API List Part 2'
  link: '../progress/ue-api-part-2.md'
---

[⬅️ **Main**](../../index.md)

# Universal Engine APIs

This page is to show all the XE APIs that are reworked in this project. The page is split into 2 parts due to the length of it's content.

## Status Legend

- 🎉 Completed
- 🚧 In Progress
- 🤧 On Hold
- 🚩 Not started yet

## Modules Overview

| #   | Module Name                    | Microservices                   | Total UE API | Total MS API | Status Progress |
| --- | ------------------------------ | ------------------------------- | ------------ | ------------ | --------------- |
| 1   | [Address](#address-module)     | Coverage                        | 1            | 1            | 🚩              |
| 2   | [Batch](#batch-module)         | Batch                           | 3            | 3            | 🚩              |
| 3   | [Catalogue](#catalogue-module) | Catalogue, Campaign & Profiling | 18           | 25           | 🚧              |

- Total UE API: 22
- Total MS API: 29
- Total reduced API: 7

## Address Module

| #   | Scope | Feature | UE API Endpoint | MS API Endpoint                                    | Status Progress |
| --- | ----- | ------- | --------------- | -------------------------------------------------- | --------------- |
| 1   | NI    | iJoin   |                 | /myunifi/coverage/v1/pre/ijoin/coordinates/address | 🚩              |

- Total UE API: 1
- Total MS API: 1

## Batch Module

| #   | Scope           | Feature   | UE Scheduler | MS Scheduler                                    | Status Progress |
| --- | --------------- | --------- | ------------ | ----------------------------------------------- | --------------- |
| 1   | NI, Inbase, SME | Scheduler |              | sendFailedNotificationEmailAtTwelveAMEveryday() | 🚩              |
| 2   | NI, Inbase, SME | Scheduler |              | fetchNotificationEntryFromOneSignal()           | 🚩              |
| 3   | NI, Inbase, SME | Scheduler |              | telegramReport()                                | 🚩              |

- Total UE Scheduler: 3
- Total MS Scheduler: 3

## Catalogue Module

| #   | Scope       | Feature       | UE API Endpoint                  | MS API Endpoint                                                                                                                                                                                                  | Status Progress |
| --- | ----------- | ------------- | -------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | Inbase      | COP           |                                  | /myunifi/catalogue/v2/irenew/products/user/recommendations                                                                                                                                                       | 🚩              |
| 2   | Inbase      | TV Pack       | /api/v1/catalogue/tvpacks        | /myunifi/catalogue/v1/ott/by-type/bundle <br>/myunifi/catalogue/v1/ott/by-type/soft-bundle <br> /myunifi/catalogue/v1/ott/by-type/swap <br> /myunifi/catalogue/v1/ott/by-type/ala-carte                          | 🎉              |
| 3   | Inbase      | TV Pack       | /api/v1/catalogue/tvpack/:planId | /myunifi/catalogue/v1/ott/by-plan-id/:ottPlanId                                                                                                                                                                  | 🎉              |
| 4   | Inbase      | TV Pack       |                                  | /myunifi/campaign/v1/get-tv-packs                                                                                                                                                                                | 🚩              |
| 5   | Inbase      | TV Pack       |                                  | /myunifi/profiling/profile/ott/available/ala-carte <br> /myunifi/profiling/profile/ott/available/swap                                                                                                            | 🚩              |
| 6   | Inbase      | OTT           |                                  | /myunifi/catalogue/v1/ott/netflix/plans                                                                                                                                                                          | 🚩              |
| 7   | Inbase      | OTT           | /api/v1/catalogue/ott            | NEW API                                                                                                                                                                                                          | 🎉              |
| 8   | Inbase      | OTT           | /api/v1/catalogue/ott/:planId    | NEW API                                                                                                                                                                                                          | 🎉              |
| 9   | Inbase      | OTT           |                                  | /myunifi/profiling/profile/ott/disney/bundle                                                                                                                                                                     | 🚩              |
| 10  | Inbase      | PME           |                                  | /myunifi/catalogue/pim/pme                                                                                                                                                                                       | 🚩              |
| 11  | Inbase      | PME           |                                  | /myunifi/catalogue/pim/register-device                                                                                                                                                                           | 🚩              |
| 12  | Inbase      | PME           |                                  | /myunifi/catalogue/pim/updateproductstatus                                                                                                                                                                       | 🚩              |
| 13  | Inbase      | FSU           |                                  | /myunifi/campaign/fsu/get-campaign-details                                                                                                                                                                       | 🚩              |
| 14  | Inbase      | Unifi Basic   |                                  | /myunifi/catalogue/v1/ipay/addon/product                                                                                                                                                                         | 🚩              |
| 15  | Inbase, SME | Device Addons |                                  | /myunifi/campaign/retrieve-campaign <br> /myunifi/campaign/v2/retrieve/addon-products <br> /myunifi/campaign/v1/retrieveServiceAddon                                                                             | 🚩              |
| 16  | SME         | Cybersecurity |                                  | /myunifi/catalogue/cybersecurity/productplan                                                                                                                                                                     | 🚩              |
| 17  | NI          | iJoin         |                                  | /myunifi/catalogue/v1/pre/ijoin/product/child/two <br> /myunifi/catalogue/v1/pre/ijoin/product/child/threefour <br> /myunifi/catalogue/v1/home/<USER>/addon <br> /myunifi/catalogue/v1/ijoin/home/<USER>/addon | 🚩              |
| 18  | NI          | iJoin         |                                  | /myunifi/catalogue/v1/ijoin/fmc/device                                                                                                                                                                           | 🚩              |

- Total UE API: 18
- Total MS API: 25

## 🛠 Enterprise API Support Contacts

If you encounter any issues with the Enterprise API, please contact the respective support teams:

📌 WERAS – 📧 <EMAIL>

📌 WSO2 – 📧 <EMAIL>

📌 OMG – 📧 <EMAIL>

For a faster resolution, include relevant details such as error messages, request payloads, and timestamps in your email. 
