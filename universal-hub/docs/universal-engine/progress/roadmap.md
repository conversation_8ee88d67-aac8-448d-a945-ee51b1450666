[⬅️ **Main**](../../index.md)

# Roadmap

This page is to show what features that has been implemented in this project.

- [x] Set up development environment with CI/CD (Github Action) and deployment (Cloudflare)
- [x] Set up database connection (Postgres)
- [ ] Set up rate limiting
- [x] Set up caching
- [x] Create Util
  - [x] Error & Log handling
  - [x] APIM token generator
- [ ] Product Catalog
  - [ ] FMC
  - [ ] Home
  - [ ] Tv Pack
  - [ ] Addon devices
- [x] Retrieve user profile details
- [x] Update user profile details
- [x] Retrieve Customer Account, Service Account
  - [x] Address
  - [x] Contact
  - [ ] TV pack/addon device or service
- [x] Retrieve BA
  - [x] Outstanding amount
  - [x] Billing history
  - [x] Payment history
  - [x] Pdf bill
  - [x] Autopay setup
- [x] Payment owner/guest create details to OSESPG
- [x] Payment transactions status update
- [x] Pay for anyone
- [ ] Favourite account
- [x] Link account
- [x] Service Request
- [x] Order Tracker
- [] Rewards
