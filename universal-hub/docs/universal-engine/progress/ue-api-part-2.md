---
prev:
  text: 'UE API List (Part 1)'
  link: '../progress/ue-api-part-1.md'
---

[⬅️ **Main**](../../index.md)

# Universal Engine APIs (Part 2)

_NOTE: All /internal APIs will be developed as methods_

## Status Legend

- 🎉 Completed
- 🚧 In Progress
- 🤧 On Hold
- 🚩 Not started yet

## Modules Overview

| #   | Module Name                          | Microservices                                     | Total UE API | Total MS API | Status Progress |
| --- | ------------------------------------ | ------------------------------------------------- | ------------ | ------------ | --------------- |
| 1   | [Eligibility](#eligibility-module)   | Campaign, Profiling, Ticketing, Ordering & Global | 12           | 18           | 🚧              |
| 2   | [Notification](#notification-module) | Notification & Ticketing                          | 6            | 11           | 🚧              |
| 3   | [Order](#order-module)               | Ordering, Ticketing, Campaign & Catalogue         | 20           | 25           | 🚩              |
| 4   | [Payment](#payment-module)           | Payment                                           | 8            | 10           | 🎉              |
| 5   | [Record](#record-module)             | Ticketing, Tracking & Service                     | 23           | 33           | 🎉              |
| 6   | [Reward](#reward-module)             | Campaign                                          | 10           | 10           | 🚩              |
| 7   | [Security](#security-module)         | Security & Notification                           | 4            | 11           | 🚩              |
| 8   | [Setting](#setting-module)           | Services                                          | 4            | 4            | 🚩              |
| 9   | [User](#user-module)                 | Profiling, Billing & Catalogue                    | 23           | 47           | 🚧              |
| 10  | [Util](#util-module)                 | Util, Global, Network & Ticketing                 | 10           | 14           | 🚧              |

- Total UE API: 121
- Total MS API: 183
- Total reduced API: 62

## Eligibility Module

| #   | Scope       | Feature       | UE API Endpoint                                | MS API Endpoint                                                                                                                                        | Status Progress |
| --- | ----------- | ------------- | ---------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------- |
| 1   | Inbase      | tNPS          | /api/v1/eligibility/survey/tnps                | /myunifi/ticket/v1/tnps/checkEligibility                                                                                                               | 🎉              |
| 2   | Inbase      | TV Pack       | /api/v1/eligibility/tvpack                     | /myunifi/profiling/services/retrieve/customer/nova/subscribed-addons                                                                                   | 🎉              |
| 3   | Inbase      | OTT           |                                                | /myunifi/profiling/profile/tvapps/get-entitlement                                                                                                      | 🚩              |
| 4   | Inbase      | FSU           | /api/v1/eligibility/fsu                        | /myunifi/campaign/fsu/get-eligible-upgrade <br> /myunifi/campaign/fsu/check-eligibility <br> /myunifi/profiling/v3/irenew/user/is-eligible-for-upgrade | 🎉              |
| 5   | Inbase      | SR            | /api/v1/eligibility/sr/termination             | /myunifi/order/transfer/check-transfer-request/v2                                                                                                      | 🎉              |
| 6   | Inbase      | Rebate        | /api/v1/eligibility/rebate                     | /myunifi/global/Season/v3/rebate-adjustment/eligibility <br> /myunifi/ticket/internal/v1/rebate/check-eligibility                                      | 🎉              |
| 7   | Inbase      | Cloud Gaming  |                                                | /myunifi/campaign/cloudgaming/check-eligibility                                                                                                        | 🚩              |
| 8   | Inbase, SME | Device Addons |                                                | /myunifi/campaign/addon/check-eligibility <br> /myunifi/campaign/check-eligibility                                                                     | 🚩              |
| 9   | SME         | DMS           | /api/v1/eligibility/ssm/sme                    | /myunifi/order/dms/retrieve/accounts/ssmcheck <br> /myunifi/order/retrieve/accounts/ssmcheck                                                           | 🎉              |
| 10  | SME         | DMS           | /api/v1/eligibility/awc-msr/sme/dms            | /myunifi/order/dms/msrcheck <br> /myunifi/order/dms/awccheck                                                                                           | 🎉              |
| 11  | SME         | Cybersecurity | /api/v1/eligibility/awc-msr/sme/cyber-security | /myunifi/order/cyberSecurity/awccheck                                                                                                                  | 🎉              |
| 12  | NI          | iJoin         |                                                | /myunifi/profiling/profile/pre/ijoin/customer/awcssmcheck                                                                                              | 🚩              |

- Total UE API: 12
- Total MS API: 18

## Notification Module

| #   | Scope           | Feature                        | UE API Endpoint    | MS API Endpoint                                                                                                                                               | Status Progress |
| --- | --------------- | ------------------------------ | ------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | NI, Inbase, SME | Email                          | getWso2SendEmail() | /myunifi/notification/send/email <br> /myunifi/notification/v2/send/email <br> /myunifi/notification/send/email-attachment <br> /myunifi/ticket/v1/send/email | 🚧              |
| 2   | NI, Inbase, SME | SMS                            |                    | /myunifi/notification/send/sms                                                                                                                                | 🚩              |
| 3   | Inbase, SME     | Feedback                       |                    | /myunifi/notification/submit-feeback-form                                                                                                                     | 🚩              |
| 4   | Inbase, SME     | Push Notification              |                    | /myunifi/notification/push-notification/retrieve <br> /myunifi/notification/push-notification/update <br> /myunifi/notification/push-notification/add         | 🚩              |
| 5   | Inbase, SME     | Personalized Push Notification |                    | /myunifi/notification/v1/personalised/push-notification/add                                                                                                   | 🚩              |
| 6   | Inbase, SME     | Opt in Push Notification       |                    | /myunifi/notification/submit                                                                                                                                  | 🚩              |

- Total UE API: 5
- Total UE Method: 1
- Total MS API: 11

## Order Module

| #   | Scope       | Feature                 | UE API Endpoint | MS API Endpoint                                                                                                        | Status Progress |
| --- | ----------- | ----------------------- | --------------- | ---------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | Inbase      | FSU                     |                 | /myunifi/campaign/fsu/check-submitted                                                                                  | 🚩              |
| 2   | Inbase, SME | Device Addons           |                 | /myunifi/order/addon/create-order <br> [No-records-found] /myunifi/order/biz/addon/create-order                        | 🚩              |
| 3   | Inbase, SME | Device Addons           |                 | /myunifi/order/checkStock                                                                                              | 🚩              |
| 4   | Inbase, SME | Device Addons           |                 | /myunifi/order/reserve/stock                                                                                           | 🚩              |
| 5   | Inbase, SME | Device Addons           |                 | /myunifi/order/validate                                                                                                | 🚩              |
| 6   | Inbase, SME | Device Addons & TV Pack |                 | /myunifi/order/checkAddonOpenOrder <br> /myunifi/order/v2/check                                                        | 🚩              |
| 7   | Inbase      | TV Pack                 |                 | /myunifi/order/tv-addon/create-order                                                                                   | 🚩              |
| 8   | Inbase      | OTT                     |                 | /myunifi/order/tvapps/swap                                                                                             | 🚩              |
| 9   | Inbase      | OTT                     |                 | /myunifi/order/tvapps/activate                                                                                         | 🚩              |
| 10  | Inbase      | OTT                     |                 | /myunifi/catalogue/v1/ott/netflix/change-plan-order                                                                    | 🚩              |
| 11  | Inbase, SME | SLOF                    |                 | /myunifi/order/v1/dynamic/slof                                                                                         | 🚩              |
| 12  | SME         | DMS                     |                 | /myunifi/order/dms/submit/order                                                                                        | 🚩              |
| 13  | SME         | Cybersecurity           |                 | /myunifi/order/cyberSecurity/slof/email                                                                                | 🚩              |
| 14  | SME         | Cybersecurity           |                 | /myunifi/order/cyberSecurity/submit/order <br> /myunifi/order/cyberSecurity/orderid                                    | 🚩              |
| 15  | NI          | SLOF                    |                 | /myunifi/order/create                                                                                                  | 🚩              |
| 16  | NI          | iJoin                   |                 | /myunifi/order/ijoin/create/order <br> /myunifi/order/ijoin/draft/order/details <br> /myunifi/order/ijoin/delete/order | 🚩              |
| 17  | NI          | iJoin                   |                 | /myunifi/order/ijoin/order/async/status                                                                                | 🚩              |
| 18  | NI          | FMC                     |                 | /myunifi/order/ijoin/fmc/create/order                                                                                  | 🚩              |
| 19  | NI          | tNPS                    |                 | /myunifi/order/ijoin/update/tnps/status                                                                                | 🚩              |
| 20  | Inbase      | OTT                     |                 | /myunifi/tracking/tvapps/order-update                                                                                  | 🚩              |

- Total UE API: 20
- Total MS API: 25

## Payment Module

| #   | Scope       | Feature | UE API Endpoint                   | MS API Endpoint                                                                                                                                 | Status Progress |
| --- | ----------- | ------- | --------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | Inbase, SME | OSES    | /api/v1/payment/oses/url          | /myunifi/payment/ipay/PFAGuestCreatedetailsOses <br> /myunifi/payment/ipay/PFAUserCreatedetailsOses <br> /myunifi/payment/v2/ipay/createdetails | 🎉              |
| 2   | Inbase, SME | OSES    | /api/v1/payment/oses/response     | /myunifi/payment/response                                                                                                                       | 🎉              |
| 3   | Inbase, SME | OSES    | /api/v1/payment/oses/status       | /myunifi/payment/v3/status <br> /myunifi/payment/v2/status/guest                                                                                | 🎉              |
| 4   | Inbase, SME | OSES    | /api/v1/payment/oses/history      | /myunifi/payment/ipay/pfaPaymentHistory                                                                                                         | 🎉              |
| 5   | Inbase, SME | Autopay | /api/v1/payment/autopay/bank-list | /myunifi/payment/autopay/banklist                                                                                                               | 🎉              |
| 6   | Inbase, SME | Autopay | /api/v1/payment/autopay/sr-status | /myunifi/payment/autopay/checkSR                                                                                                                | 🎉              |
| 7   | Inbase, SME | Autopay | /api/v1/payment/autopay/setting   | /myunifi/payment/autopay/enable                                                                                                                 | 🎉              |
| 8   | Inbase, SME | Autopay | /api/v1/payment/autopay/details   | NEW API                                                                                                                                         | 🎉              |

- Total UE API: 8
- Total MS API: 10

## Record Module

| #   | Scope  | Feature        | UE API Endpoint                                                                                                                                     | MS API Endpoint                                                                                                                             | Status Progress |
| --- | ------ | -------------- | --------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | Inbase | Easyfix, tNPS  | /api/v1/record/easyfix/survey/tnps                                                                                                                  | /myunifi/ticket/v1/tnpsSurvey                                                                                                               | 🎉              |
| 2   | Inbase | SR             | /api/v1/record/sr                                                                                                                                   | /myunifi/ticket/v2/iengage/sr/create <br> /myunifi/profiling/services/terminate                                                             | 🎉              |
| 3   | Inbase | SR, Easyfix    | /api/v1/record/easyfix/activities                                                                                                                   | [Easyfix] /myunifi/ticket/v1/iengage/sr/retrieve <br> [No-records-found] /myunifi/ticket/v1/iengage/sr/retrieve/v2                          | 🎉              |
| 4   | Inbase | SR, GOER       | /api/v1/record/activity-tracker/history                                                                                                             | [GOER] /myunifi/ticket/internal/v1/sr/retrieve                                                                                              | 🎉              |
| 5   | Inbase | GOER           | getWso2TmForceOrderProgressUpdate()                                                                                                                 | /myunifi/ticket/internal/v1/tmforce/order/progress-update                                                                                   | 🎉              |
| 6   | Inbase | GOER           | getWso2TmForceTTProgressUpdate()                                                                                                                    | /myunifi/ticket/internal/v1/tmforce/progress-update <br> [No-records-found] /myunifi/ticket/v1/tmforce/progress-update                      | 🎉              |
| 7   | Inbase | GOER           | getWso2TmForceTechinicianDetails()                                                                                                                  | /myunifi/ticket/internal/v1/tmforce/technician-details <br> [No-records-found] /myunifi/ticket/v1/tmforce/technician-details                | 🎉              |
| 8   | Inbase | GOER           | /api/v1/record/order-tracker/orderable/orders                                                                                                       | /myunifi/ticket/internal/v1/smart-order-tracking <br> [No-records-found] /myunifi/ticket/v1/smart-order-tracking                            | 🎉              |
| 9   | Inbase | GOER           | /api/v1/record/activity-tracker/history <br> /api/v1/record/order-tracker/orderable/history <br> /api/v1/record/order-tracker/non-orderable/history | /myunifi/tracking/activity/v7 <br> /myunifi/tracking/activity/v2                                                                            | 🎉              |
| 10  | Inbase | GOER           | /api/v1/record/activity-tracker/details                                                                                                             | /myunifi/tracking/v1/activity/ticket-details <br> /myunifi/order/v1/getHomeOrderDetails                                                     | 🚧              |
| 11  | Inbase | GOER           | /api/v1/record/order-tracker/orderable/appointment-slots                                                                                            | /myunifi/order/appointment/retrieve-appointment-slot                                                                                        | 🎉              |
| 12  | Inbase | GOER           | /api/v1/record/order-tracker/orderable/appointment                                                                                                  | /myunifi/order/appointment/update <br> /myunifi/order/appointment/v1/postpone-appointment <br> /myunifi/order/appointment/track-user-access | 🎉              |
| 13  | Inbase | GOER           | /api/v1/record/order-tracker/orderable/validate                                                                                                     | /myunifi/order/appointment/validate-order-tracking                                                                                          | 🎉              |
| 14  | Inbase | GOER           | /api/v1/record/order-tracker/orderable/tmf-acceptance-form                                                                                          | /myunifi/order/v1/appointment/tm-force-acceptance-form/download <br> /myunifi/order/appointment/v1/getTmForceAcceptanceForm                 | 🎉              |
| 15  | Inbase | Rebate         | wso2SubmitRebate()                                                                                                                                  | /myunifi/ticket/internal/v1/rebate/submit                                                                                                   | 🎉              |
| 16  | Inbase | Qualtrix, tNPS | /api/v1/record/survey/voc                                                                                                                           | /myunifi/ticket/v1/voc/response                                                                                                             | 🎉              |
| 17  | Inbase | Easyfix        | /api/v1/record/easyfix/sr/nova                                                                                                                      | /myunifi/ticket/v1/easyfix/sr/create/nova                                                                                                   | 🎉              |
| 18  | Inbase | Easyfix        | /api/v1/record/easyfix/sr/icp                                                                                                                       | [No-records-found] /myunifi/ticket/v1/easyfix/sr/create/icp                                                                                 | 🎉              |
| 19  | Inbase | Easyfix        | /api/v1/record/easyfix/nova/trouble-ticket                                                                                                          | /myunifi/ticket/v1/easyfix/ctt/create/nova                                                                                                  | 🎉              |
| 20  | Inbase | Easyfix        | /api/v1/record/easyfix/nova/network-trouble-ticket                                                                                                  | /myunifi/ticket/v1/easyfix/customer/retrieve/ntt/token <br> /myunifi/ticket/v1/easyfix/customer/retrieve/ntt                                | 🎉              |
| 21  | NI     | iJoin, tNPS    | /api/v1/record/survey/nes                                                                                                                           | /myunifi/ticket/v1/pre/nes/create <br> /myunifi/ticket/v1/tnps/submitTnps                                                                   | 🎉              |

- Total UE API: 19
- Total UE Method: 4
- Total MS API: 33

## Reward Module

| #   | Scope  | Feature | UE API Endpoint | MS API Endpoint                                                                         | Status Progress |
| --- | ------ | ------- | --------------- | --------------------------------------------------------------------------------------- | --------------- |
| 1   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-items                                                    | 🚩              |
| 2   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/redeem-item                                                  | 🚩              |
| 3   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-membership <br>/myunifi/campaign/v2/weras/get-membership | 🚩              |
| 4   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-customer-bills                                           | 🚩              |
| 5   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-promotion-list                                           | 🚩              |
| 6   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-my-rewards                                               | 🚩              |
| 7   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-transactions                                             | 🚩              |
| 8   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/get-online-catalogue                                         | 🚩              |
| 9   | Inbase | Weras   |                 | /myunifi/campaign/v1/weras/update-rewards-flag                                          | 🚩              |
| 10  | Inbase | Weras   |                 | /myunifi/campaign/v1/getReportEligibility                                               | 🚩              |

- Total UE API: 10
- Total MS API: 10

## Security Module

| #   | Scope           | Feature | UE API Endpoint | MS API Endpoint                                                                                                                                                                      | Status Progress |
| --- | --------------- | ------- | --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------- |
| 1   | NI, Inbase, SME | TAC     |                 | /myunifi/security/v1/store-tac <br> /myunifi/notification/send-tac <br> /myunifi/notification/v2/send-tac <br> /myunifi/notification/send/tac <br> /myunifi/notification/v2/send/tac | 🚩              |
| 2   | NI, Inbase, SME | TAC     |                 | /myunifi/security/v1/verify-tac <br> /myunifi/security/v2/verify-tac <br> /myunifi/notification/verify/tac <br> /myunifi/notification/v2/verify/tac                                  | 🚩              |
| 3   | NI, Inbase, SME | OTP     |                 | /myunifi/notification/send/otp-email                                                                                                                                                 | 🚩              |
| 4   | NI, Inbase, SME | OTP     |                 | /myunifi/notification/validate/otp                                                                                                                                                   | 🚩              |

- Total UE API: 4
- Total MS API: 11

## Setting Module

| #   | Scope  | Feature | UE API Endpoint | MS API Endpoint                 | Status Progress |
| --- | ------ | ------- | --------------- | ------------------------------- | --------------- |
| 1   | Inbase | Easyfix |                 | /myunifi/service/updateCustomer | 🚩              |
| 2   | Inbase | Easyfix |                 | /myunifi/service/spMultiaction  | 🚩              |
| 3   | Inbase | Easyfix |                 | /myunifi/service/spCnfgpwd      | 🚩              |
| 4   | Inbase | Easyfix |                 | /myunifi/service/spCnfgreqpwd   | 🚩              |

- Total UE API: 4
- Total MS API: 4

## User Module

| #   | Scope  | Feature                | UE API Endpoint                                                       | MS API Endpoint                                                                                                                                                                                                                                                                                                                                                                                                                                   | Status Progress |
| --- | ------ | ---------------------- | --------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | Inbase | Chatbot                |                                                                       | /myunifi/profiling/account/v4                                                                                                                                                                                                                                                                                                                                                                                                                     | 🤧              |
| 2   | Inbase | Personalized dashboard | /api/v1/user/billing/settings                                         | /myunifi/profiling/account/settings                                                                                                                                                                                                                                                                                                                                                                                                               | 🎉              |
| 3   | Inbase | Personalized dashboard | /api/v1/user/billing/setting/label                                    | /myunifi/profiling/account/settings                                                                                                                                                                                                                                                                                                                                                                                                               | 🎉              |
| 4   | Inbase | Personalized dashboard | /api/v1/user/billing/setting/quick-links                              | /myunifi/profiling/account/settings/quicklinks                                                                                                                                                                                                                                                                                                                                                                                                    | 🎉              |
| 5   | Inbase | Link account           | /api/v1/user/linked-account                                           | /myunifi/profiling/account/settings <br> /myunifi/profiling/profile/check/isnonowner <br> /myunifi/profiling/internal/account/nonownerinfo                                                                                                                                                                                                                                                                                                        | 🎉              |
| 6   | Inbase | Link account           | /api/v1/user/linked-account/confirmation                              | /myunifi/profiling/account/settings/confirm                                                                                                                                                                                                                                                                                                                                                                                                       | 🎉              |
| 7   | Inbase | Account Summary        | /api/v1/user/profile/accounts <br> /api/v1/user/billing/profile (GET) | /myunifi/profiling/account/v3 <br> /myunifi/profiling/account/retrieve/personal/details <br> /myunifi/profiling/internal/account/accountType<br> /myunifi/profiling/profile/tvapps/ott/get-entitlement-list <br> /myunifi/profiling/services/retrieve/customer/nova/subscribed-addons <br> /myunifi/billing/v1/ipay/unbilled <br> /myunifi/profiling/services/v3 <br> /myunifi/billing/v4 <br> /myunifi/profiling/profile/tvapps/get-subscription | 🎉              |
| 8   | Inbase | Address Details        | /api/v1/user/profile/accounts/address-details                         | /myunifi/profiling/services/retrieve-customer-info                                                                                                                                                                                                                                                                                                                                                                                                | 🎉              |
| 9   | Inbase | SR                     |                                                                       | /myunifi/profiling/profile/istay/service/details                                                                                                                                                                                                                                                                                                                                                                                                  | 🤧              |
| 10  | Inbase | KCI Periodic Discount  | /api/v1/user/billing/expiry-discount-details                          | /myunifi/profiling/profile/device-expiry-discount-details                                                                                                                                                                                                                                                                                                                                                                                         | 🎉              |
| 11  | Inbase | BA                     | /api/v1/user/billing/account                                          | /myunifi/billing/v4 <br> /myunifi/billing/v3 <br> /myunifi/billing/internal/billing-info                                                                                                                                                                                                                                                                                                                                                          | 🎉              |
| 12  | Inbase | Concise BA             | /api/v1/user/billing/concise                                          | /myunifi/internal/billing/retrieveConciseAccountDetails                                                                                                                                                                                                                                                                                                                                                                                           | 🎉              |
| 13  | Inbase | KCI Billing Info       | /api/v1/user/billing/kci-info                                         | /myunifi/internal/billing/getBillInfo/v2                                                                                                                                                                                                                                                                                                                                                                                                          | 🎉              |
| 14  | Inbase | Update Billing Profile | /api/v1/user/billing/profile (PUT)                                    | /myunifi/billing/updatebillingprofile/v3                                                                                                                                                                                                                                                                                                                                                                                                          | 🎉              |
| 15  | Inbase | CA Nova Profile        | /api/v1/user/billing/nova/profile                                     | /myunifi/billing/v1/retrieve-billing-profile                                                                                                                                                                                                                                                                                                                                                                                                      | 🎉              |
| 16  | Inbase | Annual Statement       | /api/v1/user/billing/annual-statement                                 | /myunifi/billing/getBillingSummary                                                                                                                                                                                                                                                                                                                                                                                                                | 🎉              |
| 17  | Inbase | iBill                  | /api/v1/user/billing/ibill-details                                    | /myunifi/billing/v2/ibill/getbilldetails <br> /myunifi/billing/v3/iBill/GetBillDetails                                                                                                                                                                                                                                                                                                                                                            | 🎉              |
| 18  | Inbase | Oustanding Amount      | getWso2OutstandingAmount()                                            | /myunifi/billing/outstanding-amount                                                                                                                                                                                                                                                                                                                                                                                                               | 🎉              |
| 19  | Inbase | Account Verification   | getWso2BAVerification()                                               | /myunifi/billing/accountVerification                                                                                                                                                                                                                                                                                                                                                                                                              | 🎉              |
| 20  | Inbase | Favourite Account      |                                                                       | /myunifi/profiling/account/settings/favourite-account                                                                                                                                                                                                                                                                                                                                                                                             | 🤧              |
| 21  | Inbase | Broadband Usage        |                                                                       | /myunifi/profiling/broadband/usage                                                                                                                                                                                                                                                                                                                                                                                                                | 🤧              |
| 22  | Inbase | Truck Roll Status      |                                                                       | /myunifi/profiling/v1/irenew/user/is-no-truck-roll-required                                                                                                                                                                                                                                                                                                                                                                                       | 🤧              |
| 23  | Inbase | TV Pack                |                                                                       | /myunifi/profiling/profile/tvapps/get-pending-activation                                                                                                                                                                                                                                                                                                                                                                                          | 🤧              |
| 24  | Inbase | TV Pack                |                                                                       | /myunifi/profiling/profile/tvapps/user/verify                                                                                                                                                                                                                                                                                                                                                                                                     | 🤧              |
| 25  | Inbase | OTT                    |                                                                       | /myunifi/profiling/profile/tvapps/disney-check-eligible <br> /myunifi/profiling/profile/tvapps/disney-change-mobile                                                                                                                                                                                                                                                                                                                               | 🤧              |
| 26  | Inbase | OTT                    |                                                                       | /myunifi/catalogue/v1/ott/netflix/request-activation-url <br> /myunifi/catalogue/v1/ott/hbo/request-activation-url                                                                                                                                                                                                                                                                                                                                | 🤧              |
| 27  | Inbase | OTT                    |                                                                       | /myunifi/catalogue/v1/ott/netflix/request-account-recovery                                                                                                                                                                                                                                                                                                                                                                                        | 🤧              |
| 28  | Inbase | OTT                    |                                                                       | /myunifi/profiling/profile/tvapps/get-netflix-plan                                                                                                                                                                                                                                                                                                                                                                                                | 🤧              |
| 29  | Inbase | Contract Renewal       |                                                                       | /myunifi/profiling/internal/services/v2/contract-renewal                                                                                                                                                                                                                                                                                                                                                                                          | 🤧              |
| 30  | SME    | SA                     |                                                                       | /myunifi/profile/v1/service-account/has-access-to-service-id                                                                                                                                                                                                                                                                                                                                                                                      | 🚩              |
| 31  | SME    | Cloud connect          | /api/v1/user/sme/cloud-connect                                        | /myunifi/profiling/services/cloud/connect/registration                                                                                                                                                                                                                                                                                                                                                                                            | 🎉              |
| 32  | SME    | Ecommerce              | /api/v1/user/sme/eCommerce                                            | /myunifi/profiling/profile/create/account                                                                                                                                                                                                                                                                                                                                                                                                         | 🎉              |
| 33  | SME    | DMS                    | /api/v1/user/sme/dms-credit-score                                     | /myunifi/profile/dms/credit/score                                                                                                                                                                                                                                                                                                                                                                                                                 | 🎉              |
| 34  | Inbase | Easyfix                | /api/v1/user/easyfix/nova/customer                                    | /myunifi/ticket/v1/easyfix/customer/create/nova                                                                                                                                                                                                                                                                                                                                                                                                   | 🎉              |
| 35  | Inbase | Easyfix                | /api/v1/user/easyfix/nova/customer/details                            | /myunifi/ticket/v1/easyfix/customer/retrieve/nova                                                                                                                                                                                                                                                                                                                                                                                                 | 🎉              |

- Total UE API: 21
- Total UE Method: 2
- Total MS API: 47

## Util Module

| #   | Scope           | Feature                 | UE API Endpoint       | MS API Endpoint                                                                                                                               | Status Progress |
| --- | --------------- | ----------------------- | --------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| 1   | NI, Inbase, SME | APIM Token              | getWso2ApimToken()    | getApimToken()                                                                                                                                | 🎉              |
| 2   | Inbase          | LOV                     | /api/v1/util/lov/list | /myunifi/ticket/v1/terminatelist <br> /myunifi/ticket/v1/complaintlist <br> /myunifi/global/lov <br> [No-records-found]/myunifi/global/v2/lov | 🎉              |
| 3   | Inbase          | Maintenance config      |                       | /myunifi/global/v1/maintenance-configs/:platform                                                                                              | 🚩              |
| 4   | Inbase          | Get apps latest version |                       | /myunifi/global/forceupdate                                                                                                                   | 🚩              |
| 5   | Inbase          | Get app deeplink        |                       | /myunifi/global/app/deeplink                                                                                                                  | 🚩              |
| 6   | Inbase          | Get landing page        |                       | /myunifi/global/app/home                                                                                                                      | 🚩              |
| 7   | Inbase          | Season                  |                       | /myunifi/global/Season/fun-unifi/retrieveContent <br> /myunifi/global/Season/LigaMalaysia/retriveurlvideo                                     | 🚩              |
| 8   | Inbase          | Rebate                  | /api/v1/record/rebate | /myunifi/global/Season/v3/rebate-adjustment/claims                                                                                            | 🎉              |
| 9   | Inbase          | Network alerts          |                       | /myunifi/xe-network/v1/alerts                                                                                                                 | 🚩              |
| 10  | Inbase          | Maintenance page        |                       | /myunifi/global/app/page-maintenance                                                                                                          | 🚩              |

- Total UE API: 9
- Total UE Method: 1
- Total MS API: 13
- Total MS Method: 1
